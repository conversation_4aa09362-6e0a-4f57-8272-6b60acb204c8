import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { SubscriptionService } from '../services/subscription.service';
import {
  SubscribeRequest,
  GetSubscriptionsQuery,
  SubscriptionFilters,
} from '../types';
import { <PERSON>rrorLogger } from '../lib/error-utils';
import { SubscriptionNotifications } from '../utils/subscriptionNotifications';

/**
 * Query keys for subscription-related queries
 */
export const subscriptionKeys = {
  all: ['subscriptions'] as const,
  plans: () => [...subscriptionKeys.all, 'plans'] as const,
  plansWithQuery: (query?: GetSubscriptionsQuery) => [...subscriptionKeys.plans(), query] as const,
  filteredPlans: (filters?: SubscriptionFilters) => [...subscriptionKeys.plans(), 'filtered', filters] as const,
  userSubscription: () => [...subscriptionKeys.all, 'user'] as const,
  stats: () => [...subscriptionKeys.all, 'stats'] as const,
  statusInfo: () => [...subscriptionKeys.all, 'status-info'] as const,
  creditBalance: () => [...subscriptionKeys.all, 'credits'] as const,
};

/**
 * Hook for fetching subscription plans (public endpoint)
 */
export const useSubscriptionPlans = (query?: GetSubscriptionsQuery) => {
  return useQuery({
    queryKey: subscriptionKeys.plansWithQuery(query),
    queryFn: () => SubscriptionService.getSubscriptionPlans(query),
    staleTime: 10 * 60 * 1000, // 10 minutes (plans don't change often)
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionPlans' });
    },
  });
};

/**
 * Hook for fetching filtered subscription plans
 */
export const useFilteredSubscriptionPlans = (filters?: SubscriptionFilters) => {
  return useQuery({
    queryKey: subscriptionKeys.filteredPlans(filters),
    queryFn: () => SubscriptionService.getFilteredSubscriptionPlans(filters),
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchFilteredSubscriptionPlans' });
    },
  });
};

/**
 * Hook for fetching a single subscription plan by ID
 */
export const useSubscriptionPlan = (id: string) => {
  return useQuery({
    queryKey: [...subscriptionKeys.plans(), id],
    queryFn: () => SubscriptionService.getSubscriptionById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
    gcTime: 15 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionPlan' });
    },
  });
};

/**
 * Hook for fetching user's current subscription
 */
export const useUserSubscription = () => {
  return useQuery({
    queryKey: subscriptionKeys.userSubscription(),
    queryFn: () => SubscriptionService.getUserSubscription(),
    staleTime: 2 * 60 * 1000, // 2 minutes (user data changes more frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchUserSubscription' });
    },
  });
};

/**
 * Hook for checking if user has active subscription
 */
export const useHasActiveSubscription = () => {
  return useQuery({
    queryKey: [...subscriptionKeys.userSubscription(), 'active'],
    queryFn: () => SubscriptionService.hasActiveSubscription(),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'checkActiveSubscription' });
    },
  });
};

/**
 * Hook for fetching user's credit balance
 */
export const useCreditBalance = () => {
  return useQuery({
    queryKey: subscriptionKeys.creditBalance(),
    queryFn: () => SubscriptionService.getCreditBalance(),
    staleTime: 1 * 60 * 1000, // 1 minute (credits change frequently)
    gcTime: 3 * 60 * 1000, // 3 minutes
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchCreditBalance' });
    },
  });
};

/**
 * Hook for fetching subscription status information
 */
export const useSubscriptionStatusInfo = () => {
  return useQuery({
    queryKey: subscriptionKeys.statusInfo(),
    queryFn: () => SubscriptionService.getSubscriptionStatusInfo(),
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionStatusInfo' });
    },
  });
};

/**
 * Hook for fetching subscription statistics
 */
export const useSubscriptionStats = () => {
  return useQuery({
    queryKey: subscriptionKeys.stats(),
    queryFn: () => SubscriptionService.getSubscriptionStats(),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    onError: (error: any) => {
      ErrorLogger.log(error, { context: 'fetchSubscriptionStats' });
    },
  });
};

/**
 * Hook for subscribing to a plan
 */
export const useSubscribeToplan = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscribeRequest) => SubscriptionService.subscribeToplan(data),
    onSuccess: (response) => {
      // Invalidate all subscription-related queries to refresh data
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.userSubscription() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.statusInfo() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.creditBalance() });
      queryClient.invalidateQueries({ queryKey: subscriptionKeys.stats() });

      // Show success notification
      const planName = response.data.subscription.subscription?.name || 'subscription plan';
      const creditsAdded = response.data.updatedCredits || response.data.subscription.creditsAllocated || 0;
      SubscriptionNotifications.showSubscriptionSuccess(planName, creditsAdded);
    },
    onError: (error: any) => {
      const message = error?.message || 'Failed to subscribe to plan';
      ErrorLogger.log(error, { context: 'subscribeToplan' });
      SubscriptionNotifications.showSubscriptionError(message);
    },
  });
};

/**
 * Hook for refreshing subscription data
 */
export const useRefreshSubscriptionData = () => {
  const queryClient = useQueryClient();

  return () => {
    // Invalidate all subscription queries to force refresh
    queryClient.invalidateQueries({ queryKey: subscriptionKeys.all });
  };
};

/**
 * Hook for prefetching subscription plans
 */
export const usePrefetchSubscriptionPlans = () => {
  const queryClient = useQueryClient();

  return (query?: GetSubscriptionsQuery) => {
    queryClient.prefetchQuery({
      queryKey: subscriptionKeys.plansWithQuery(query),
      queryFn: () => SubscriptionService.getSubscriptionPlans(query),
      staleTime: 10 * 60 * 1000,
    });
  };
};

/**
 * Hook for optimistic subscription updates
 */
export const useOptimisticSubscriptionUpdate = () => {
  const queryClient = useQueryClient();

  return {
    updateCreditBalance: (newBalance: number) => {
      queryClient.setQueryData(subscriptionKeys.creditBalance(), newBalance);
    },
    updateSubscriptionStatus: (status: 'active' | 'expired' | 'cancelled' | 'pending') => {
      queryClient.setQueryData(
        subscriptionKeys.userSubscription(),
        (oldData: any) => {
          if (!oldData?.data?.subscription) return oldData;
          return {
            ...oldData,
            data: {
              ...oldData.data,
              subscription: {
                ...oldData.data.subscription,
                status,
              },
            },
          };
        }
      );
    },
  };
};
