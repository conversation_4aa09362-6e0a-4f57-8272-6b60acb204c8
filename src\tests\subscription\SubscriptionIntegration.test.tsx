/**
 * Subscription Integration Tests
 * 
 * End-to-end tests for the complete subscription workflow including:
 * - Subscription page rendering
 * - Plan selection and purchase flow
 * - Subscription management actions
 * - Navigation and routing
 * - Error scenarios
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import SubscriptionManagement from '../../pages/Subscription/SubscriptionManagement';
import { SubscriptionPlanSelectionFlow } from '../../components/subscription';
import { SubscriptionService } from '../../services/subscription.service';
import { SubscriptionNotifications } from '../../utils/subscriptionNotifications';
import { 
  Subscription, 
  UserSubscription,
  SubscriptionPlansResponse,
  UserSubscriptionResponse,
  SubscribeResponse 
} from '../../types';

// Mock the SubscriptionService
vi.mock('../../services/subscription.service', () => ({
  SubscriptionService: {
    getSubscriptionPlans: vi.fn(),
    getFilteredSubscriptionPlans: vi.fn(),
    getUserSubscription: vi.fn(),
    subscribeToplan: vi.fn(),
    getSubscriptionStatusInfo: vi.fn(),
  },
}));

// Mock the SubscriptionNotifications
vi.mock('../../utils/subscriptionNotifications', () => ({
  SubscriptionNotifications: {
    showSubscriptionSuccess: vi.fn(),
    showSubscriptionError: vi.fn(),
    showLowCreditsWarning: vi.fn(),
    showExpiryWarning: vi.fn(),
  },
}));

// Mock the SubscriptionRetry
vi.mock('../../utils/subscriptionRetry', () => ({
  SubscriptionRetry: {
    retryFetchPlans: vi.fn((fn) => fn()),
    retryUserSubscription: vi.fn((fn) => fn()),
    retrySubscription: vi.fn((fn) => fn()),
    createQueryRetry: vi.fn(() => ({ retry: 2 })),
    createMutationRetry: vi.fn(() => ({ retry: 1 })),
    getErrorMessage: vi.fn((error) => error.message || 'Unknown error'),
  },
}));

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  },
}));

const mockSubscriptions: Subscription[] = [
  {
    id: 'basic-plan',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    name: 'Basic Plan',
    description: 'Perfect for getting started',
    price: 999, // $9.99
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 50,
    features: ['Basic Support', '50 Credits'],
    isActive: true,
  },
  {
    id: 'pro-plan',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    name: 'Pro Plan',
    description: 'For growing businesses',
    price: 2999, // $29.99
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 200,
    features: ['Priority Support', '200 Credits', 'Advanced Analytics'],
    isActive: true,
  },
];

const mockUserSubscription: UserSubscription = {
  id: 'user-sub-1',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  userId: 'user-123',
  subscriptionId: 'pro-plan',
  status: 'active',
  startDate: '2024-01-15T10:00:00Z',
  endDate: '2024-02-15T10:00:00Z',
  creditsAllocated: 200,
  subscription: mockSubscriptions[1],
};

const mockPlansResponse: SubscriptionPlansResponse = {
  success: true,
  message: 'Success',
  data: {
    subscriptions: mockSubscriptions,
    pagination: { page: 1, limit: 10, totalCount: 2, totalPages: 1 },
  },
};

const mockUserSubscriptionResponse: UserSubscriptionResponse = {
  success: true,
  message: 'Success',
  data: {
    subscription: mockUserSubscription,
    credits: 150,
  },
};

const mockSubscribeResponse: SubscribeResponse = {
  success: true,
  message: 'Success',
  data: {
    subscription: mockUserSubscription,
    updatedCredits: 200,
  },
};

const mockStatusInfo = {
  hasActiveSubscription: true,
  currentPlan: mockSubscriptions[1],
  creditsRemaining: 150,
  status: 'active' as const,
  daysUntilExpiry: 15,
  isExpiringSoon: false,
  canUpgrade: true,
  canDowngrade: true,
};

// Test wrapper component
const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Subscription Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock implementations
    vi.mocked(SubscriptionService.getSubscriptionPlans).mockResolvedValue(mockPlansResponse);
    vi.mocked(SubscriptionService.getFilteredSubscriptionPlans).mockResolvedValue(mockSubscriptions);
    vi.mocked(SubscriptionService.getUserSubscription).mockResolvedValue(mockUserSubscriptionResponse);
    vi.mocked(SubscriptionService.getSubscriptionStatusInfo).mockResolvedValue(mockStatusInfo);
  });

  describe('Subscription Management Page', () => {
    it('renders subscription management page with active subscription', async () => {
      renderWithProviders(<SubscriptionManagement />);

      // Check page title and breadcrumb
      expect(screen.getByText('Subscription Management')).toBeInTheDocument();
      
      // Wait for subscription data to load
      await waitFor(() => {
        expect(screen.getByText('Pro Plan')).toBeInTheDocument();
      });

      // Check subscription status
      expect(screen.getByText('Active')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument(); // Credits remaining
      
      // Check action buttons
      expect(screen.getByText('View All Plans')).toBeInTheDocument();
      expect(screen.getByText('Manage Subscription')).toBeInTheDocument();
    });

    it('renders subscription management page without subscription', async () => {
      const noSubResponse = {
        ...mockUserSubscriptionResponse,
        data: { subscription: null, credits: 10 },
      };
      const noSubStatusInfo = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
        currentPlan: undefined,
        status: 'none' as const,
      };

      vi.mocked(SubscriptionService.getUserSubscription).mockResolvedValue(noSubResponse);
      vi.mocked(SubscriptionService.getSubscriptionStatusInfo).mockResolvedValue(noSubStatusInfo);

      renderWithProviders(<SubscriptionManagement />);

      await waitFor(() => {
        expect(screen.getByText('Choose a Plan')).toBeInTheDocument();
      });

      // Should show available plans section
      expect(screen.getByText('Available Plans')).toBeInTheDocument();
    });

    it('handles loading states correctly', () => {
      // Mock loading state
      vi.mocked(SubscriptionService.getUserSubscription).mockImplementation(
        () => new Promise(() => {}) // Never resolves
      );

      renderWithProviders(<SubscriptionManagement />);

      // Should show loading spinner
      expect(screen.getByRole('status')).toBeInTheDocument();
    });

    it('handles error states correctly', async () => {
      const error = new Error('Failed to load subscription data');
      vi.mocked(SubscriptionService.getUserSubscription).mockRejectedValue(error);

      renderWithProviders(<SubscriptionManagement />);

      await waitFor(() => {
        expect(screen.getByText('Failed to load subscription data')).toBeInTheDocument();
      });
    });
  });

  describe('Subscription Plan Selection Flow', () => {
    it('completes subscription purchase flow successfully', async () => {
      const user = userEvent.setup();
      const onClose = vi.fn();
      const onSuccess = vi.fn();

      vi.mocked(SubscriptionService.subscribeToplan).mockResolvedValue(mockSubscribeResponse);

      renderWithProviders(
        <SubscriptionPlanSelectionFlow
          isOpen={true}
          onClose={onClose}
          onSuccess={onSuccess}
        />
      );

      // Wait for plans to load
      await waitFor(() => {
        expect(screen.getByText('Basic Plan')).toBeInTheDocument();
        expect(screen.getByText('Pro Plan')).toBeInTheDocument();
      });

      // Click on Pro Plan subscribe button
      const subscribeButtons = screen.getAllByText('Subscribe');
      await user.click(subscribeButtons[1]); // Pro Plan button

      // Confirmation dialog should appear
      await waitFor(() => {
        expect(screen.getByText('Confirm Subscription')).toBeInTheDocument();
      });

      // Confirm subscription
      const confirmButton = screen.getByText('Subscribe Now');
      await user.click(confirmButton);

      // Should call the subscription service
      await waitFor(() => {
        expect(SubscriptionService.subscribeToplan).toHaveBeenCalledWith({
          subscriptionId: 'pro-plan',
        });
      });

      // Should show success notification
      expect(SubscriptionNotifications.showSubscriptionSuccess).toHaveBeenCalledWith(
        'Pro Plan',
        200
      );

      // Should call success callback
      expect(onSuccess).toHaveBeenCalledWith(mockSubscribeResponse);
    });

    it('handles subscription errors correctly', async () => {
      const user = userEvent.setup();
      const onClose = vi.fn();

      const error = new Error('Payment failed');
      vi.mocked(SubscriptionService.subscribeToplan).mockRejectedValue(error);

      renderWithProviders(
        <SubscriptionPlanSelectionFlow
          isOpen={true}
          onClose={onClose}
        />
      );

      // Wait for plans to load and click subscribe
      await waitFor(() => {
        expect(screen.getByText('Basic Plan')).toBeInTheDocument();
      });

      const subscribeButtons = screen.getAllByText('Subscribe');
      await user.click(subscribeButtons[0]); // Basic Plan button

      // Confirm subscription
      await waitFor(() => {
        expect(screen.getByText('Subscribe Now')).toBeInTheDocument();
      });

      const confirmButton = screen.getByText('Subscribe Now');
      await user.click(confirmButton);

      // Should show error notification
      await waitFor(() => {
        expect(SubscriptionNotifications.showSubscriptionError).toHaveBeenCalledWith(
          'Payment failed'
        );
      });

      // Should return to plan selection
      expect(screen.getByText('Choose Your Plan')).toBeInTheDocument();
    });

    it('filters plans correctly', async () => {
      const user = userEvent.setup();

      renderWithProviders(
        <SubscriptionPlanSelectionFlow
          isOpen={true}
          onClose={vi.fn()}
        />
      );

      // Wait for plans to load
      await waitFor(() => {
        expect(screen.getByText('Basic Plan')).toBeInTheDocument();
        expect(screen.getByText('Pro Plan')).toBeInTheDocument();
      });

      // Click monthly filter (should be selected by default)
      const monthlyFilter = screen.getByText('Monthly');
      await user.click(monthlyFilter);

      // Both plans should still be visible (both are monthly)
      expect(screen.getByText('Basic Plan')).toBeInTheDocument();
      expect(screen.getByText('Pro Plan')).toBeInTheDocument();
    });
  });

  describe('Navigation Integration', () => {
    it('opens subscription modal when clicking action buttons', async () => {
      const user = userEvent.setup();

      renderWithProviders(<SubscriptionManagement />);

      // Wait for page to load
      await waitFor(() => {
        expect(screen.getByText('Manage Subscription')).toBeInTheDocument();
      });

      // Click manage subscription button
      const manageButton = screen.getByText('Manage Subscription');
      await user.click(manageButton);

      // Modal should open (we can't easily test modal opening without more complex setup)
      // In a real test, you'd verify the modal state or mock the useModal hook
    });
  });

  describe('Error Recovery', () => {
    it('retries failed requests', async () => {
      // First call fails, second succeeds
      vi.mocked(SubscriptionService.getUserSubscription)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockUserSubscriptionResponse);

      renderWithProviders(<SubscriptionManagement />);

      // Should eventually show subscription data after retry
      await waitFor(() => {
        expect(screen.getByText('Pro Plan')).toBeInTheDocument();
      }, { timeout: 3000 });
    });
  });
});
