/**
 * Subscription Accessibility Utilities
 * 
 * Utilities for improving accessibility and responsive design
 * in subscription components
 */

/**
 * Generate accessible labels for subscription plans
 */
export const generatePlanAccessibilityLabels = (plan: {
  id: string;
  name: string;
  price: number;
  interval: string;
  creditsIncluded: number;
  features: string[];
}) => {
  const priceText = formatPrice(plan.price);
  const intervalText = plan.interval === 'monthly' ? 'per month' : 
                     plan.interval === 'yearly' ? 'per year' : 
                     plan.interval === 'one-time' ? 'one-time payment' : 
                     `per ${plan.interval}`;

  return {
    cardLabel: `${plan.name} subscription plan`,
    cardDescription: `${plan.name} plan for ${priceText} ${intervalText} with ${plan.creditsIncluded} credits and ${plan.features.length} features`,
    priceLabel: `Price: ${priceText} ${intervalText}`,
    creditsLabel: `${plan.creditsIncluded.toLocaleString()} credits included`,
    featuresLabel: `${plan.features.length} features included`,
    subscribeButtonLabel: `Subscribe to ${plan.name} plan for ${priceText} ${intervalText}`,
  };
};

/**
 * Generate accessible labels for subscription status
 */
export const generateStatusAccessibilityLabels = (subscription: {
  status: string;
  subscription?: { name: string; price?: number; interval?: string };
  endDate?: string;
  creditsRemaining?: number;
}) => {
  const statusText = getStatusDisplayText(subscription.status);
  const planName = subscription.subscription?.name || 'subscription plan';
  const daysUntilExpiry = subscription.endDate ? getDaysUntilExpiry(subscription.endDate) : null;
  
  let expiryText = '';
  if (daysUntilExpiry !== null) {
    if (daysUntilExpiry <= 0) {
      expiryText = 'expired';
    } else if (daysUntilExpiry === 1) {
      expiryText = 'expires tomorrow';
    } else {
      expiryText = `expires in ${daysUntilExpiry} days`;
    }
  }

  return {
    statusLabel: `Subscription status: ${statusText}`,
    planLabel: `Current plan: ${planName}`,
    expiryLabel: expiryText ? `Subscription ${expiryText}` : '',
    creditsLabel: subscription.creditsRemaining 
      ? `${subscription.creditsRemaining.toLocaleString()} credits remaining`
      : 'No credits remaining',
    summaryLabel: `${planName} subscription is ${statusText}${expiryText ? `, ${expiryText}` : ''}`,
  };
};

/**
 * Format price for accessibility
 */
export const formatPrice = (price: number): string => {
  const dollars = Math.floor(price / 100);
  const cents = price % 100;
  
  if (cents === 0) {
    return `$${dollars}`;
  }
  
  return `$${dollars}.${cents.toString().padStart(2, '0')}`;
};

/**
 * Get display text for subscription status
 */
export const getStatusDisplayText = (status: string): string => {
  switch (status) {
    case 'active':
      return 'active';
    case 'expired':
      return 'expired';
    case 'cancelled':
      return 'cancelled';
    case 'pending':
      return 'pending activation';
    case 'suspended':
      return 'suspended';
    default:
      return status;
  }
};

/**
 * Calculate days until expiry
 */
export const getDaysUntilExpiry = (endDate: string): number | null => {
  try {
    const now = new Date();
    const expiry = new Date(endDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  } catch {
    return null;
  }
};

/**
 * Generate keyboard navigation helpers
 */
export const createKeyboardNavigation = () => {
  const handleKeyDown = (event: React.KeyboardEvent, callback: () => void) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      callback();
    }
  };

  const handleArrowNavigation = (
    event: React.KeyboardEvent,
    currentIndex: number,
    totalItems: number,
    onNavigate: (newIndex: number) => void
  ) => {
    let newIndex = currentIndex;
    
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        newIndex = (currentIndex + 1) % totalItems;
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        newIndex = currentIndex === 0 ? totalItems - 1 : currentIndex - 1;
        break;
      case 'Home':
        newIndex = 0;
        break;
      case 'End':
        newIndex = totalItems - 1;
        break;
      default:
        return;
    }
    
    event.preventDefault();
    onNavigate(newIndex);
  };

  return {
    handleKeyDown,
    handleArrowNavigation,
  };
};

/**
 * Generate responsive breakpoint classes
 */
export const getResponsiveClasses = () => {
  return {
    // Grid layouts
    planGrid: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6',
    statusGrid: 'grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6',
    
    // Flex layouts
    headerFlex: 'flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4',
    buttonFlex: 'flex flex-col sm:flex-row gap-3',
    badgeFlex: 'flex items-center gap-2 flex-wrap',
    
    // Padding and spacing
    cardPadding: 'p-4 sm:p-6',
    sectionSpacing: 'space-y-4 sm:space-y-6',
    
    // Typography
    titleText: 'text-lg sm:text-xl font-bold',
    priceText: 'text-3xl sm:text-4xl font-bold',
    bodyText: 'text-sm sm:text-base',
    captionText: 'text-xs sm:text-sm',
    
    // Interactive elements
    button: 'px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base',
    filterButton: 'px-3 py-2 text-sm font-medium',
    
    // Focus states
    focusRing: 'focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50',
    focusVisible: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand-500 focus-visible:ring-opacity-50',
  };
};

/**
 * Generate ARIA attributes for subscription components
 */
export const generateAriaAttributes = (type: 'plan' | 'status' | 'filter' | 'action', data: any) => {
  const baseAttributes = {
    role: type === 'plan' ? 'article' : 
          type === 'status' ? 'region' : 
          type === 'filter' ? 'group' : 
          'button',
  };

  switch (type) {
    case 'plan':
      return {
        ...baseAttributes,
        'aria-labelledby': `plan-${data.id}-title`,
        'aria-describedby': `plan-${data.id}-description`,
      };
    
    case 'status':
      return {
        ...baseAttributes,
        'aria-labelledby': 'subscription-status-title',
        'aria-live': 'polite',
        'aria-atomic': 'true',
      };
    
    case 'filter':
      return {
        ...baseAttributes,
        'aria-label': `Filter subscription plans by ${data.filterType}`,
      };
    
    case 'action':
      return {
        ...baseAttributes,
        'aria-label': data.label,
        'aria-describedby': data.describedBy,
        'aria-pressed': data.pressed,
      };
    
    default:
      return baseAttributes;
  }
};

/**
 * Screen reader announcements for subscription actions
 */
export const createScreenReaderAnnouncements = () => {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  return {
    subscriptionSuccess: (planName: string) => 
      announce(`Successfully subscribed to ${planName}`, 'assertive'),
    
    subscriptionError: (error: string) => 
      announce(`Subscription failed: ${error}`, 'assertive'),
    
    planSelected: (planName: string) => 
      announce(`${planName} plan selected`),
    
    filterApplied: (filterType: string, filterValue: string) => 
      announce(`Filter applied: showing ${filterValue} ${filterType} plans`),
    
    statusUpdated: (status: string) => 
      announce(`Subscription status updated to ${status}`),
  };
};

/**
 * Mobile-specific optimizations
 */
export const getMobileOptimizations = () => {
  return {
    // Touch targets (minimum 44px)
    touchTarget: 'min-h-[44px] min-w-[44px]',
    
    // Improved tap areas
    tapArea: 'p-3 -m-3',
    
    // Mobile-friendly spacing
    mobileSpacing: 'space-y-3 sm:space-y-4',
    
    // Mobile typography
    mobileText: 'text-base leading-relaxed',
    
    // Mobile buttons
    mobileButton: 'w-full py-3 text-base font-medium',
    
    // Mobile cards
    mobileCard: 'mx-4 sm:mx-0',
  };
};

export default {
  generatePlanAccessibilityLabels,
  generateStatusAccessibilityLabels,
  formatPrice,
  getStatusDisplayText,
  getDaysUntilExpiry,
  createKeyboardNavigation,
  getResponsiveClasses,
  generateAriaAttributes,
  createScreenReaderAnnouncements,
  getMobileOptimizations,
};
