/**
 * SubscriptionService API Tests
 * 
 * Tests for the SubscriptionService class including:
 * - Subscription plans fetching
 * - User subscription management
 * - Subscription purchase operations
 * - Error scenarios
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SubscriptionService } from '../../services/subscription.service';
import { apiClient } from '../../lib/api-client';
import { 
  Subscription, 
  UserSubscription, 
  SubscribeRequest,
  SubscriptionPlansResponse,
  UserSubscriptionResponse,
  SubscribeResponse 
} from '../../types';

// Mock the API client
vi.mock('../../lib/api-client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
  },
}));

// Mock the config
vi.mock('../../lib/config', () => ({
  config: {
    endpoints: {
      subscriptions: {
        plans: '/api/subscriptions',
        userSubscription: '/api/auth/user/subscription',
        subscribe: '/api/auth/subscriptions/subscribe',
      },
    },
  },
}));

const mockSubscription: Subscription = {
  id: 'sub-1',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  name: 'Pro Plan',
  description: 'Professional subscription with advanced features',
  price: 2999, // $29.99
  duration: 30,
  interval: 'monthly',
  creditsIncluded: 100,
  features: ['Advanced Analytics', 'Priority Support', 'API Access'],
  isActive: true,
};

const mockUserSubscription: UserSubscription = {
  id: 'user-sub-1',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  userId: 'user-123',
  subscriptionId: 'sub-1',
  status: 'active',
  startDate: '2024-01-15T10:00:00Z',
  endDate: '2024-02-15T10:00:00Z',
  creditsAllocated: 100,
  subscription: mockSubscription,
};

const mockPlansResponse: SubscriptionPlansResponse = {
  success: true,
  message: 'Subscription plans retrieved successfully',
  data: {
    subscriptions: [mockSubscription],
    pagination: {
      page: 1,
      limit: 10,
      totalCount: 1,
      totalPages: 1,
    },
  },
};

const mockUserSubscriptionResponse: UserSubscriptionResponse = {
  success: true,
  message: 'User subscription retrieved successfully',
  data: {
    subscription: mockUserSubscription,
    credits: 85,
  },
};

const mockSubscribeResponse: SubscribeResponse = {
  success: true,
  message: 'Successfully subscribed to plan',
  data: {
    subscription: mockUserSubscription,
    updatedCredits: 100,
  },
};

describe('SubscriptionService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getSubscriptionPlans', () => {
    it('fetches subscription plans successfully', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockPlansResponse });

      const result = await SubscriptionService.getSubscriptionPlans();

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/subscriptions',
        { params: undefined }
      );
      expect(result).toEqual(mockPlansResponse);
    });

    it('passes query parameters correctly', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockPlansResponse });
      const query = { isActive: true, page: 1, limit: 5 };

      await SubscriptionService.getSubscriptionPlans(query);

      expect(apiClient.get).toHaveBeenCalledWith(
        '/api/subscriptions',
        { params: query }
      );
    });

    it('handles API errors', async () => {
      const error = new Error('Network error');
      vi.mocked(apiClient.get).mockRejectedValue(error);

      await expect(SubscriptionService.getSubscriptionPlans()).rejects.toThrow('Network error');
    });
  });

  describe('getUserSubscription', () => {
    it('fetches user subscription successfully', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockUserSubscriptionResponse });

      const result = await SubscriptionService.getUserSubscription();

      expect(apiClient.get).toHaveBeenCalledWith('/api/auth/user/subscription');
      expect(result).toEqual(mockUserSubscriptionResponse);
    });

    it('handles no subscription case', async () => {
      const noSubResponse = {
        ...mockUserSubscriptionResponse,
        data: { subscription: null, credits: 0 },
      };
      vi.mocked(apiClient.get).mockResolvedValue({ data: noSubResponse });

      const result = await SubscriptionService.getUserSubscription();

      expect(result.data.subscription).toBeNull();
      expect(result.data.credits).toBe(0);
    });
  });

  describe('subscribeToplan', () => {
    it('subscribes to plan successfully', async () => {
      vi.mocked(apiClient.post).mockResolvedValue({ data: mockSubscribeResponse });
      
      const subscribeData: SubscribeRequest = {
        subscriptionId: 'sub-1',
        paymentMethodDetails: { paymentMethodId: 'pm-123' },
      };

      const result = await SubscriptionService.subscribeToplan(subscribeData);

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/auth/subscriptions/subscribe',
        subscribeData
      );
      expect(result).toEqual(mockSubscribeResponse);
    });

    it('handles subscription errors', async () => {
      const error = new Error('Payment failed');
      vi.mocked(apiClient.post).mockRejectedValue(error);

      const subscribeData: SubscribeRequest = { subscriptionId: 'sub-1' };

      await expect(SubscriptionService.subscribeToplan(subscribeData)).rejects.toThrow('Payment failed');
    });
  });

  describe('getFilteredSubscriptionPlans', () => {
    it('filters plans by interval', async () => {
      const monthlyPlan = { ...mockSubscription, interval: 'monthly' as const };
      const yearlyPlan = { ...mockSubscription, id: 'sub-2', interval: 'yearly' as const };
      
      const response = {
        ...mockPlansResponse,
        data: {
          ...mockPlansResponse.data,
          subscriptions: [monthlyPlan, yearlyPlan],
        },
      };

      vi.mocked(apiClient.get).mockResolvedValue({ data: response });

      const result = await SubscriptionService.getFilteredSubscriptionPlans({ interval: 'monthly' });

      expect(result).toHaveLength(1);
      expect(result[0].interval).toBe('monthly');
    });

    it('filters plans by price range', async () => {
      const cheapPlan = { ...mockSubscription, price: 999 }; // $9.99
      const expensivePlan = { ...mockSubscription, id: 'sub-2', price: 4999 }; // $49.99
      
      const response = {
        ...mockPlansResponse,
        data: {
          ...mockPlansResponse.data,
          subscriptions: [cheapPlan, expensivePlan],
        },
      };

      vi.mocked(apiClient.get).mockResolvedValue({ data: response });

      const result = await SubscriptionService.getFilteredSubscriptionPlans({ 
        minPrice: 1000, 
        maxPrice: 3000 
      });

      expect(result).toHaveLength(0); // No plans in the $10-$30 range
    });

    it('filters plans by search term', async () => {
      const proPlan = { ...mockSubscription, name: 'Pro Plan' };
      const basicPlan = { ...mockSubscription, id: 'sub-2', name: 'Basic Plan' };
      
      const response = {
        ...mockPlansResponse,
        data: {
          ...mockPlansResponse.data,
          subscriptions: [proPlan, basicPlan],
        },
      };

      vi.mocked(apiClient.get).mockResolvedValue({ data: response });

      const result = await SubscriptionService.getFilteredSubscriptionPlans({ search: 'pro' });

      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Pro Plan');
    });
  });

  describe('getSubscriptionById', () => {
    it('returns subscription when found', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockPlansResponse });

      const result = await SubscriptionService.getSubscriptionById('sub-1');

      expect(result).toEqual(mockSubscription);
    });

    it('returns null when subscription not found', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockPlansResponse });

      const result = await SubscriptionService.getSubscriptionById('non-existent');

      expect(result).toBeNull();
    });

    it('returns null on API error', async () => {
      vi.mocked(apiClient.get).mockRejectedValue(new Error('API error'));

      const result = await SubscriptionService.getSubscriptionById('sub-1');

      expect(result).toBeNull();
    });
  });

  describe('hasActiveSubscription', () => {
    it('returns true for active subscription', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockUserSubscriptionResponse });

      const result = await SubscriptionService.hasActiveSubscription();

      expect(result).toBe(true);
    });

    it('returns false for non-active subscription', async () => {
      const expiredResponse = {
        ...mockUserSubscriptionResponse,
        data: {
          ...mockUserSubscriptionResponse.data,
          subscription: { ...mockUserSubscription, status: 'expired' as const },
        },
      };
      vi.mocked(apiClient.get).mockResolvedValue({ data: expiredResponse });

      const result = await SubscriptionService.hasActiveSubscription();

      expect(result).toBe(false);
    });

    it('returns false on error', async () => {
      vi.mocked(apiClient.get).mockRejectedValue(new Error('API error'));

      const result = await SubscriptionService.hasActiveSubscription();

      expect(result).toBe(false);
    });
  });

  describe('getCreditBalance', () => {
    it('returns credit balance', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockUserSubscriptionResponse });

      const result = await SubscriptionService.getCreditBalance();

      expect(result).toBe(85);
    });

    it('returns 0 when no credits', async () => {
      const noCreditsResponse = {
        ...mockUserSubscriptionResponse,
        data: { ...mockUserSubscriptionResponse.data, credits: 0 },
      };
      vi.mocked(apiClient.get).mockResolvedValue({ data: noCreditsResponse });

      const result = await SubscriptionService.getCreditBalance();

      expect(result).toBe(0);
    });

    it('returns 0 on error', async () => {
      vi.mocked(apiClient.get).mockRejectedValue(new Error('API error'));

      const result = await SubscriptionService.getCreditBalance();

      expect(result).toBe(0);
    });
  });

  describe('getSubscriptionStatusInfo', () => {
    it('returns status info for active subscription', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockUserSubscriptionResponse });

      const result = await SubscriptionService.getSubscriptionStatusInfo();

      expect(result.hasActiveSubscription).toBe(true);
      expect(result.currentPlan).toEqual(mockSubscription);
      expect(result.creditsRemaining).toBe(85);
      expect(result.status).toBe('active');
    });

    it('returns status info for no subscription', async () => {
      const noSubResponse = {
        ...mockUserSubscriptionResponse,
        data: { subscription: null, credits: 10 },
      };
      vi.mocked(apiClient.get).mockResolvedValue({ data: noSubResponse });

      const result = await SubscriptionService.getSubscriptionStatusInfo();

      expect(result.hasActiveSubscription).toBe(false);
      expect(result.currentPlan).toBeUndefined();
      expect(result.creditsRemaining).toBe(10);
      expect(result.status).toBe('none');
    });
  });
});
