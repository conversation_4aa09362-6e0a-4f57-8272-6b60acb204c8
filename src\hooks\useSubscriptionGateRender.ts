import React from 'react';
import { useSubscriptionGate } from './useSubscriptionMonitoring';
import SubscriptionGate from '../components/subscription/SubscriptionGate';

/**
 * Hook for conditionally rendering subscription-gated content
 */
export function useSubscriptionGateRender() {
  const { canPerformAction, hasActiveSubscription, creditsRemaining } = useSubscriptionGate();

  const renderGated = (
    content: React.ReactNode,
    options: {
      creditsRequired?: number;
      fallback?: React.ReactNode;
      upgradeMessage?: string;
    } = {}
  ) => {
    const { creditsRequired = 1, fallback, upgradeMessage } = options;
    
    if (canPerformAction(creditsRequired)) {
      return content;
    }

    return React.createElement(
      SubscriptionGate,
      {
        creditsRequired,
        fallback,
        upgradeMessage,
      },
      content
    );
  };

  return {
    renderGated,
    canPerformAction,
    hasActiveSubscription,
    creditsRemaining,
  };
}
