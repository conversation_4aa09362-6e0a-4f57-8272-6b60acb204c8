import React from 'react';
import Button from '../ui/button/Button';
import { Subscription, UserSubscription } from '../../types';

interface SubscriptionStatusProps {
  subscription: UserSubscription | null;
  creditsRemaining: number;
  onUpgrade?: () => void;
  onManage?: () => void;
  isLoading?: boolean;
  className?: string;
}

export default function SubscriptionStatus({
  subscription,
  creditsRemaining,
  onUpgrade,
  onManage,
  isLoading = false,
  className = '',
}: SubscriptionStatusProps) {
  
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100); // Convert from cents to dollars
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'expired':
        return 'Expired';
      case 'cancelled':
        return 'Cancelled';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  };

  const getDaysUntilExpiry = (endDate?: string): number | null => {
    if (!endDate) return null;
    const now = new Date();
    const expiry = new Date(endDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const isExpiringSoon = (endDate?: string): boolean => {
    const daysUntilExpiry = getDaysUntilExpiry(endDate);
    return daysUntilExpiry !== null && daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  };

  const getCreditStatusColor = (credits: number): string => {
    if (credits <= 10) {
      return 'text-red-600 dark:text-red-400';
    } else if (credits <= 50) {
      return 'text-yellow-600 dark:text-yellow-400';
    }
    return 'text-green-600 dark:text-green-400';
  };

  // No subscription case
  if (!subscription) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No Active Subscription
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Subscribe to a plan to get started with credits and premium features.
          </p>
          
          {/* Credits remaining */}
          <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getCreditStatusColor(creditsRemaining)}`}>
                {creditsRemaining.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Credits Remaining
              </div>
            </div>
          </div>

          {onUpgrade && (
            <Button
              variant="primary"
              size="md"
              onClick={onUpgrade}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Loading...' : 'Choose a Plan'}
            </Button>
          )}
        </div>
      </div>
    );
  }

  const daysUntilExpiry = getDaysUntilExpiry(subscription.endDate);
  const expiringSoon = isExpiringSoon(subscription.endDate);

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-4 sm:p-6 ${className}`}
      role="region"
      aria-labelledby="subscription-status-title"
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-6 gap-4">
        <div className="min-w-0 flex-1">
          <h3
            id="subscription-status-title"
            className="text-lg font-semibold text-gray-900 dark:text-white"
          >
            {subscription.subscription?.name || 'Subscription Plan'}
          </h3>
          <div className="flex items-center gap-2 mt-1 flex-wrap">
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(subscription.status)}`}
              aria-label={`Subscription status: ${getStatusText(subscription.status)}`}
            >
              {getStatusText(subscription.status)}
            </span>
            {expiringSoon && (
              <span
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400"
                aria-label="Subscription expires soon"
              >
                Expires Soon
              </span>
            )}
          </div>
        </div>
        
        {subscription.subscription?.price && (
          <div className="text-right">
            <div className="text-lg font-bold text-gray-900 dark:text-white">
              {formatPrice(subscription.subscription.price)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              /{subscription.subscription.interval}
            </div>
          </div>
        )}
      </div>

      {/* Credits */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <div className={`text-2xl font-bold ${getCreditStatusColor(creditsRemaining)}`}>
              {creditsRemaining.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Credits Remaining
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-gray-900 dark:text-white">
              {subscription.creditsAllocated.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Total Allocated
            </div>
          </div>
        </div>
        
        {/* Credit usage bar */}
        <div className="mt-3">
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
            <span>Used</span>
            <span>Remaining</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
            <div
              className="bg-brand-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${Math.max(0, Math.min(100, ((subscription.creditsAllocated - creditsRemaining) / subscription.creditsAllocated) * 100))}%`
              }}
            />
          </div>
        </div>
      </div>

      {/* Subscription Details */}
      <div className="space-y-3 mb-6">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">Started</span>
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {formatDate(subscription.startDate)}
          </span>
        </div>
        
        {subscription.endDate && (
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {subscription.status === 'active' ? 'Expires' : 'Expired'}
            </span>
            <span className={`text-sm font-medium ${expiringSoon ? 'text-orange-600 dark:text-orange-400' : 'text-gray-900 dark:text-white'}`}>
              {formatDate(subscription.endDate)}
              {daysUntilExpiry !== null && daysUntilExpiry > 0 && (
                <span className="ml-1 text-xs">
                  ({daysUntilExpiry} days)
                </span>
              )}
            </span>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        {onUpgrade && subscription.status === 'active' && (
          <Button
            variant="primary"
            size="sm"
            onClick={onUpgrade}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? 'Loading...' : 'Upgrade'}
          </Button>
        )}
        
        {onManage && (
          <Button
            variant="outline"
            size="sm"
            onClick={onManage}
            disabled={isLoading}
            className="flex-1"
          >
            Manage
          </Button>
        )}
        
        {!subscription.endDate && subscription.status === 'active' && (
          <div className="flex-1">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              One-time purchase
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
