import React from 'react';

/**
 * Loading skeleton for subscription plan cards
 */
export const SubscriptionPlanCardSkeleton: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
    {/* Header */}
    <div className="text-center mb-6">
      <div className="flex items-center justify-center gap-2 mb-2">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
        <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-16"></div>
      </div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mx-auto mb-4"></div>
      
      {/* Price */}
      <div className="mb-2">
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-20 mx-auto"></div>
      </div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
    </div>

    {/* Credits */}
    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
      <div className="text-center">
        <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16 mx-auto mb-1"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-20 mx-auto"></div>
      </div>
    </div>

    {/* Features */}
    <div className="mb-6">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-3"></div>
      <div className="space-y-2">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-start">
            <div className="w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-full mr-2 mt-0.5"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
          </div>
        ))}
      </div>
    </div>

    {/* Button */}
    <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
  </div>
);

/**
 * Loading skeleton for subscription status component
 */
export const SubscriptionStatusSkeleton: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
    {/* Header */}
    <div className="flex items-start justify-between mb-6">
      <div>
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
        <div className="flex items-center gap-2">
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-16"></div>
          <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
        </div>
      </div>
      <div className="text-right">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 mb-1"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
      </div>
    </div>

    {/* Credits */}
    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-16 mb-1"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
        </div>
        <div className="text-right">
          <div className="h-6 bg-gray-200 dark:bg-gray-600 rounded w-12 mb-1"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-16"></div>
        </div>
      </div>
      <div className="mt-3">
        <div className="flex justify-between text-xs mb-1">
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-8"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-12"></div>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"></div>
      </div>
    </div>

    {/* Details */}
    <div className="space-y-3 mb-6">
      <div className="flex items-center justify-between">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
      </div>
      <div className="flex items-center justify-between">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
      </div>
    </div>

    {/* Buttons */}
    <div className="flex gap-3">
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
    </div>
  </div>
);

/**
 * Loading skeleton for subscription plans grid
 */
export const SubscriptionPlansGridSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => (
  <div>
    {/* Filters skeleton */}
    <div className="mb-6">
      <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse mb-4"></div>
      <div className="flex gap-2">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        ))}
      </div>
    </div>
    
    {/* Grid skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <SubscriptionPlanCardSkeleton key={i} />
      ))}
    </div>
  </div>
);

/**
 * Loading skeleton for subscription management actions
 */
export const SubscriptionManagementActionsSkeleton: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-4"></div>
    
    <div className="space-y-4">
      {/* Action buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded flex-1"></div>
      </div>
      
      {/* Divider */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full mb-2"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto"></div>
      </div>
      
      {/* Info section */}
      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="space-y-1">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    </div>
  </div>
);

/**
 * Loading skeleton for dashboard subscription widget
 */
export const SubscriptionWidgetSkeleton: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 animate-pulse">
    <div className="flex items-start justify-between mb-4">
      <div>
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-1"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
      </div>
      <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
    </div>

    {/* Credits */}
    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded w-12 mb-1"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
        </div>
        <div className="text-right">
          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-8 mb-1"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-600 rounded w-12"></div>
        </div>
      </div>
      <div className="mt-3">
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5"></div>
      </div>
    </div>

    {/* Expiry info */}
    <div className="flex items-center justify-between text-sm mb-4">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12"></div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
    </div>

    {/* Button */}
    <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
  </div>
);

/**
 * Loading skeleton for subscription modal content
 */
export const SubscriptionModalSkeleton: React.FC = () => (
  <div className="p-6">
    {/* Header */}
    <div className="mb-6">
      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2 animate-pulse"></div>
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-64 animate-pulse"></div>
    </div>
    
    {/* Content */}
    <SubscriptionPlansGridSkeleton />
  </div>
);

/**
 * Inline loading spinner for subscription operations
 */
export const SubscriptionSpinner: React.FC<{
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-brand-500 border-t-transparent ${sizeClasses[size]} ${className}`} />
  );
};

/**
 * Full page loading state for subscription pages
 */
export const SubscriptionPageLoading: React.FC<{
  message?: string;
}> = ({ message = 'Loading subscription data...' }) => (
  <div className="flex items-center justify-center min-h-96">
    <div className="text-center">
      <SubscriptionSpinner size="lg" className="mx-auto mb-4" />
      <p className="text-gray-600 dark:text-gray-400">{message}</p>
    </div>
  </div>
);

export default {
  SubscriptionPlanCardSkeleton,
  SubscriptionStatusSkeleton,
  SubscriptionPlansGridSkeleton,
  SubscriptionManagementActionsSkeleton,
  SubscriptionWidgetSkeleton,
  SubscriptionModalSkeleton,
  SubscriptionSpinner,
  SubscriptionPageLoading,
};
