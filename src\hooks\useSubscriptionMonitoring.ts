import { useEffect, useCallback } from 'react';
import { useSubscriptionStatusInfo, useCreditBalance } from './useSubscriptions';
import { SubscriptionNotifications } from '../utils/subscriptionNotifications';

interface SubscriptionMonitoringOptions {
  enableLowCreditWarnings?: boolean;
  enableExpiryWarnings?: boolean;
  lowCreditThreshold?: number;
  expiryWarningDays?: number;
  checkInterval?: number; // in milliseconds
}

const DEFAULT_OPTIONS: Required<SubscriptionMonitoringOptions> = {
  enableLowCreditWarnings: true,
  enableExpiryWarnings: true,
  lowCreditThreshold: 10,
  expiryWarningDays: 7,
  checkInterval: 5 * 60 * 1000, // 5 minutes
};

/**
 * Hook for monitoring subscription status and showing relevant notifications
 */
export const useSubscriptionMonitoring = (options: SubscriptionMonitoringOptions = {}) => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  
  const { data: statusInfo, isLoading: statusLoading } = useSubscriptionStatusInfo();
  const { data: creditBalance, isLoading: creditsLoading } = useCreditBalance();

  // Track if we've already shown warnings to avoid spam
  const hasShownLowCreditWarning = useCallback(() => {
    const key = 'subscription_low_credit_warning_shown';
    const lastShown = localStorage.getItem(key);
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    
    if (!lastShown || (now - parseInt(lastShown)) > oneHour) {
      localStorage.setItem(key, now.toString());
      return false;
    }
    return true;
  }, []);

  const hasShownExpiryWarning = useCallback(() => {
    const key = 'subscription_expiry_warning_shown';
    const lastShown = localStorage.getItem(key);
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    
    if (!lastShown || (now - parseInt(lastShown)) > oneDay) {
      localStorage.setItem(key, now.toString());
      return false;
    }
    return true;
  }, []);

  // Check for low credits
  useEffect(() => {
    if (
      config.enableLowCreditWarnings &&
      !creditsLoading &&
      creditBalance !== undefined &&
      creditBalance <= config.lowCreditThreshold &&
      creditBalance > 0 &&
      !hasShownLowCreditWarning()
    ) {
      SubscriptionNotifications.showLowCreditsWarning(creditBalance, config.lowCreditThreshold);
    }
  }, [creditBalance, creditsLoading, config.enableLowCreditWarnings, config.lowCreditThreshold, hasShownLowCreditWarning]);

  // Check for subscription expiry
  useEffect(() => {
    if (
      config.enableExpiryWarnings &&
      !statusLoading &&
      statusInfo?.isExpiringSoon &&
      statusInfo?.daysUntilExpiry &&
      statusInfo.daysUntilExpiry <= config.expiryWarningDays &&
      !hasShownExpiryWarning()
    ) {
      SubscriptionNotifications.showExpiryWarning(
        statusInfo.daysUntilExpiry,
        statusInfo.currentPlan?.name
      );
    }
  }, [statusInfo, statusLoading, config.enableExpiryWarnings, config.expiryWarningDays, hasShownExpiryWarning]);

  // Check for expired subscription
  useEffect(() => {
    if (
      !statusLoading &&
      statusInfo?.status === 'expired' &&
      !hasShownExpiryWarning()
    ) {
      SubscriptionNotifications.showExpiryWarning(0, statusInfo.currentPlan?.name);
    }
  }, [statusInfo, statusLoading, hasShownExpiryWarning]);

  return {
    statusInfo,
    creditBalance,
    isLoading: statusLoading || creditsLoading,
    hasActiveSubscription: statusInfo?.hasActiveSubscription || false,
    isExpiringSoon: statusInfo?.isExpiringSoon || false,
    isLowOnCredits: creditBalance !== undefined && creditBalance <= config.lowCreditThreshold,
    canUpgrade: statusInfo?.canUpgrade || false,
    canDowngrade: statusInfo?.canDowngrade || false,
  };
};

/**
 * Hook for checking if user can perform credit-consuming actions
 */
export const useSubscriptionGate = () => {
  const { data: statusInfo } = useSubscriptionStatusInfo();
  const { data: creditBalance } = useCreditBalance();

  const canPerformAction = useCallback((creditsRequired: number = 1): boolean => {
    // Check if user has active subscription
    if (!statusInfo?.hasActiveSubscription) {
      return false;
    }

    // Check if user has enough credits
    if (creditBalance === undefined || creditBalance < creditsRequired) {
      return false;
    }

    return true;
  }, [statusInfo, creditBalance]);

  const showUpgradePrompt = useCallback((action: string, creditsRequired: number = 1) => {
    if (!statusInfo?.hasActiveSubscription) {
      SubscriptionNotifications.showFeatureAccessDenied(action);
      return;
    }

    if (creditBalance !== undefined && creditBalance < creditsRequired) {
      SubscriptionNotifications.showInsufficientCredits(action, creditsRequired, creditBalance);
      return;
    }
  }, [statusInfo, creditBalance]);

  return {
    canPerformAction,
    showUpgradePrompt,
    hasActiveSubscription: statusInfo?.hasActiveSubscription || false,
    creditsRemaining: creditBalance || 0,
    isLoading: !statusInfo || creditBalance === undefined,
  };
};

/**
 * Hook for subscription-aware feature flags
 */
export const useSubscriptionFeatures = () => {
  const { data: statusInfo } = useSubscriptionStatusInfo();

  const hasFeature = useCallback((feature: string): boolean => {
    // Basic features available to all users
    const basicFeatures = [
      'basic_appointments',
      'basic_customers',
      'basic_services',
    ];

    if (basicFeatures.includes(feature)) {
      return true;
    }

    // Premium features require active subscription
    const premiumFeatures = [
      'advanced_analytics',
      'bulk_operations',
      'custom_branding',
      'priority_support',
      'api_access',
      'integrations',
    ];

    if (premiumFeatures.includes(feature)) {
      return statusInfo?.hasActiveSubscription || false;
    }

    // Unknown feature - default to requiring subscription
    return statusInfo?.hasActiveSubscription || false;
  }, [statusInfo]);

  const getFeatureLimit = useCallback((feature: string): number | null => {
    if (!statusInfo?.hasActiveSubscription) {
      // Free tier limits
      switch (feature) {
        case 'appointments_per_month':
          return 50;
        case 'customers':
          return 100;
        case 'services':
          return 5;
        default:
          return null;
      }
    }

    // Premium tier - no limits or higher limits
    switch (feature) {
      case 'appointments_per_month':
        return null; // Unlimited
      case 'customers':
        return null; // Unlimited
      case 'services':
        return null; // Unlimited
      default:
        return null;
    }
  }, [statusInfo]);

  return {
    hasFeature,
    getFeatureLimit,
    hasActiveSubscription: statusInfo?.hasActiveSubscription || false,
    currentPlan: statusInfo?.currentPlan,
    isLoading: !statusInfo,
  };
};
