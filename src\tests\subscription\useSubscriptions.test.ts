/**
 * Subscription Hooks Tests
 * 
 * Tests for React Query hooks including:
 * - Data fetching hooks
 * - Mutation hooks
 * - Error handling
 * - Query invalidation
 * - Loading states
 */

import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  useSubscriptionPlans,
  useUserSubscription,
  useSubscribeToplan,
  useCreditBalance,
  useSubscriptionStatusInfo,
} from '../../hooks/useSubscriptions';
import { SubscriptionService } from '../../services/subscription.service';
import { SubscriptionNotifications } from '../../utils/subscriptionNotifications';
import { 
  Subscription, 
  UserSubscription, 
  SubscribeRequest,
  SubscriptionPlansResponse,
  UserSubscriptionResponse,
  SubscribeResponse 
} from '../../types';

// Mock the SubscriptionService
vi.mock('../../services/subscription.service', () => ({
  SubscriptionService: {
    getSubscriptionPlans: vi.fn(),
    getFilteredSubscriptionPlans: vi.fn(),
    getUserSubscription: vi.fn(),
    subscribeToplan: vi.fn(),
    getCreditBalance: vi.fn(),
    getSubscriptionStatusInfo: vi.fn(),
  },
}));

// Mock the SubscriptionNotifications
vi.mock('../../utils/subscriptionNotifications', () => ({
  SubscriptionNotifications: {
    showSubscriptionSuccess: vi.fn(),
    showSubscriptionError: vi.fn(),
  },
}));

// Mock the SubscriptionRetry
vi.mock('../../utils/subscriptionRetry', () => ({
  SubscriptionRetry: {
    retryFetchPlans: vi.fn((fn) => fn()),
    retryUserSubscription: vi.fn((fn) => fn()),
    retrySubscription: vi.fn((fn) => fn()),
    createQueryRetry: vi.fn(() => ({ retry: 2 })),
    createMutationRetry: vi.fn(() => ({ retry: 1 })),
    getErrorMessage: vi.fn((error) => error.message || 'Unknown error'),
  },
}));

const mockSubscription: Subscription = {
  id: 'sub-1',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  name: 'Pro Plan',
  description: 'Professional subscription',
  price: 2999,
  duration: 30,
  interval: 'monthly',
  creditsIncluded: 100,
  features: ['Advanced Analytics', 'Priority Support'],
  isActive: true,
};

const mockUserSubscription: UserSubscription = {
  id: 'user-sub-1',
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  userId: 'user-123',
  subscriptionId: 'sub-1',
  status: 'active',
  startDate: '2024-01-15T10:00:00Z',
  endDate: '2024-02-15T10:00:00Z',
  creditsAllocated: 100,
  subscription: mockSubscription,
};

const mockPlansResponse: SubscriptionPlansResponse = {
  success: true,
  message: 'Success',
  data: {
    subscriptions: [mockSubscription],
    pagination: { page: 1, limit: 10, totalCount: 1, totalPages: 1 },
  },
};

const mockUserSubscriptionResponse: UserSubscriptionResponse = {
  success: true,
  message: 'Success',
  data: {
    subscription: mockUserSubscription,
    credits: 85,
  },
};

const mockSubscribeResponse: SubscribeResponse = {
  success: true,
  message: 'Success',
  data: {
    subscription: mockUserSubscription,
    updatedCredits: 100,
  },
};

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Subscription Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useSubscriptionPlans', () => {
    it('fetches subscription plans successfully', async () => {
      vi.mocked(SubscriptionService.getSubscriptionPlans).mockResolvedValue(mockPlansResponse);

      const { result } = renderHook(() => useSubscriptionPlans(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockPlansResponse);
      expect(SubscriptionService.getSubscriptionPlans).toHaveBeenCalledWith(undefined);
    });

    it('passes query parameters correctly', async () => {
      vi.mocked(SubscriptionService.getSubscriptionPlans).mockResolvedValue(mockPlansResponse);
      const query = { isActive: true, page: 1 };

      const { result } = renderHook(() => useSubscriptionPlans(query), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(SubscriptionService.getSubscriptionPlans).toHaveBeenCalledWith(query);
    });

    it('handles errors correctly', async () => {
      const error = new Error('Network error');
      vi.mocked(SubscriptionService.getSubscriptionPlans).mockRejectedValue(error);

      const { result } = renderHook(() => useSubscriptionPlans(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('useUserSubscription', () => {
    it('fetches user subscription successfully', async () => {
      vi.mocked(SubscriptionService.getUserSubscription).mockResolvedValue(mockUserSubscriptionResponse);

      const { result } = renderHook(() => useUserSubscription(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUserSubscriptionResponse);
    });

    it('handles no subscription case', async () => {
      const noSubResponse = {
        ...mockUserSubscriptionResponse,
        data: { subscription: null, credits: 0 },
      };
      vi.mocked(SubscriptionService.getUserSubscription).mockResolvedValue(noSubResponse);

      const { result } = renderHook(() => useUserSubscription(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data?.data.subscription).toBeNull();
    });
  });

  describe('useCreditBalance', () => {
    it('fetches credit balance successfully', async () => {
      vi.mocked(SubscriptionService.getCreditBalance).mockResolvedValue(85);

      const { result } = renderHook(() => useCreditBalance(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBe(85);
    });
  });

  describe('useSubscriptionStatusInfo', () => {
    it('fetches status info successfully', async () => {
      const statusInfo = {
        hasActiveSubscription: true,
        currentPlan: mockSubscription,
        creditsRemaining: 85,
        status: 'active' as const,
        isExpiringSoon: false,
        canUpgrade: true,
        canDowngrade: true,
      };
      vi.mocked(SubscriptionService.getSubscriptionStatusInfo).mockResolvedValue(statusInfo);

      const { result } = renderHook(() => useSubscriptionStatusInfo(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(statusInfo);
    });
  });

  describe('useSubscribeToplan', () => {
    it('subscribes to plan successfully', async () => {
      vi.mocked(SubscriptionService.subscribeToplan).mockResolvedValue(mockSubscribeResponse);

      const { result } = renderHook(() => useSubscribeToplan(), {
        wrapper: createWrapper(),
      });

      const subscribeData: SubscribeRequest = { subscriptionId: 'sub-1' };
      
      result.current.mutate(subscribeData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(SubscriptionService.subscribeToplan).toHaveBeenCalledWith(subscribeData);
      expect(SubscriptionNotifications.showSubscriptionSuccess).toHaveBeenCalledWith(
        'Pro Plan',
        100
      );
    });

    it('handles subscription errors', async () => {
      const error = new Error('Payment failed');
      vi.mocked(SubscriptionService.subscribeToplan).mockRejectedValue(error);

      const { result } = renderHook(() => useSubscribeToplan(), {
        wrapper: createWrapper(),
      });

      const subscribeData: SubscribeRequest = { subscriptionId: 'sub-1' };
      
      result.current.mutate(subscribeData);

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(SubscriptionNotifications.showSubscriptionError).toHaveBeenCalledWith(
        'Payment failed'
      );
    });

    it('invalidates queries on success', async () => {
      vi.mocked(SubscriptionService.subscribeToplan).mockResolvedValue(mockSubscribeResponse);

      const queryClient = new QueryClient();
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(() => useSubscribeToplan(), { wrapper });

      const subscribeData: SubscribeRequest = { subscriptionId: 'sub-1' };
      
      result.current.mutate(subscribeData);

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ 
        queryKey: ['subscriptions', 'user'] 
      });
    });
  });
});
