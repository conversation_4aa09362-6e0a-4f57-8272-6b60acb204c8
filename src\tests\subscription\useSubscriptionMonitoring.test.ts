/**
 * Subscription Monitoring Hooks Tests
 * 
 * Tests for subscription monitoring functionality including:
 * - Subscription status monitoring
 * - Credit balance monitoring
 * - Subscription gates
 * - Feature flags
 */

import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  useSubscriptionMonitoring,
  useSubscriptionGate,
  useSubscriptionFeatures,
} from '../../hooks/useSubscriptionMonitoring';
import { useSubscriptionStatusInfo, useCreditBalance } from '../../hooks/useSubscriptions';
import { SubscriptionNotifications } from '../../utils/subscriptionNotifications';

// Mock the subscription hooks
vi.mock('../../hooks/useSubscriptions', () => ({
  useSubscriptionStatusInfo: vi.fn(),
  useCreditBalance: vi.fn(),
}));

// Mock the SubscriptionNotifications
vi.mock('../../utils/subscriptionNotifications', () => ({
  SubscriptionNotifications: {
    showLowCreditsWarning: vi.fn(),
    showExpiryWarning: vi.fn(),
    showFeatureAccessDenied: vi.fn(),
    showInsufficientCredits: vi.fn(),
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const mockStatusInfo = {
  hasActiveSubscription: true,
  currentPlan: {
    id: 'sub-1',
    name: 'Pro Plan',
    price: 2999,
    interval: 'monthly' as const,
    creditsIncluded: 100,
    features: ['Advanced Analytics', 'Priority Support'],
  },
  creditsRemaining: 85,
  status: 'active' as const,
  daysUntilExpiry: 15,
  isExpiringSoon: false,
  canUpgrade: true,
  canDowngrade: true,
};

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Subscription Monitoring Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('useSubscriptionMonitoring', () => {
    it('monitors subscription status successfully', async () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 85,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionMonitoring(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasActiveSubscription).toBe(true);
      expect(result.current.creditBalance).toBe(85);
      expect(result.current.isExpiringSoon).toBe(false);
      expect(result.current.isLowOnCredits).toBe(false);
    });

    it('shows low credits warning when credits are low', async () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 5, // Low credits
        isLoading: false,
        error: null,
      } as any);

      // Mock localStorage to simulate first warning
      localStorageMock.getItem.mockReturnValue(null);

      renderHook(() => useSubscriptionMonitoring(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(SubscriptionNotifications.showLowCreditsWarning).toHaveBeenCalledWith(5, 10);
      });
    });

    it('shows expiry warning when subscription is expiring soon', async () => {
      const expiringSoonStatus = {
        ...mockStatusInfo,
        daysUntilExpiry: 3,
        isExpiringSoon: true,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: expiringSoonStatus,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 85,
        isLoading: false,
        error: null,
      } as any);

      // Mock localStorage to simulate first warning
      localStorageMock.getItem.mockReturnValue(null);

      renderHook(() => useSubscriptionMonitoring(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(SubscriptionNotifications.showExpiryWarning).toHaveBeenCalledWith(
          3,
          'Pro Plan'
        );
      });
    });

    it('does not show duplicate warnings', async () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 5,
        isLoading: false,
        error: null,
      } as any);

      // Mock localStorage to simulate warning already shown recently
      localStorageMock.getItem.mockReturnValue(Date.now().toString());

      renderHook(() => useSubscriptionMonitoring(), {
        wrapper: createWrapper(),
      });

      // Wait a bit to ensure no notifications are called
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(SubscriptionNotifications.showLowCreditsWarning).not.toHaveBeenCalled();
    });
  });

  describe('useSubscriptionGate', () => {
    it('allows action when user has sufficient credits', () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 85,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionGate(), {
        wrapper: createWrapper(),
      });

      expect(result.current.canPerformAction(10)).toBe(true);
      expect(result.current.hasActiveSubscription).toBe(true);
      expect(result.current.creditsRemaining).toBe(85);
    });

    it('blocks action when user has insufficient credits', () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 5,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionGate(), {
        wrapper: createWrapper(),
      });

      expect(result.current.canPerformAction(10)).toBe(false);
    });

    it('blocks action when user has no active subscription', () => {
      const noSubStatus = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: noSubStatus,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 85,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionGate(), {
        wrapper: createWrapper(),
      });

      expect(result.current.canPerformAction(10)).toBe(false);
    });

    it('shows appropriate upgrade prompts', () => {
      const noSubStatus = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: noSubStatus,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 85,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionGate(), {
        wrapper: createWrapper(),
      });

      result.current.showUpgradePrompt('create appointment', 1);

      expect(SubscriptionNotifications.showFeatureAccessDenied).toHaveBeenCalledWith(
        'create appointment'
      );
    });

    it('shows insufficient credits prompt', () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      vi.mocked(useCreditBalance).mockReturnValue({
        data: 5,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionGate(), {
        wrapper: createWrapper(),
      });

      result.current.showUpgradePrompt('create appointment', 10);

      expect(SubscriptionNotifications.showInsufficientCredits).toHaveBeenCalledWith(
        'create appointment',
        10,
        5
      );
    });
  });

  describe('useSubscriptionFeatures', () => {
    it('allows basic features for all users', () => {
      const noSubStatus = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: noSubStatus,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionFeatures(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasFeature('basic_appointments')).toBe(true);
      expect(result.current.hasFeature('basic_customers')).toBe(true);
      expect(result.current.hasFeature('basic_services')).toBe(true);
    });

    it('restricts premium features for non-subscribers', () => {
      const noSubStatus = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: noSubStatus,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionFeatures(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasFeature('advanced_analytics')).toBe(false);
      expect(result.current.hasFeature('api_access')).toBe(false);
      expect(result.current.hasFeature('priority_support')).toBe(false);
    });

    it('allows premium features for subscribers', () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionFeatures(), {
        wrapper: createWrapper(),
      });

      expect(result.current.hasFeature('advanced_analytics')).toBe(true);
      expect(result.current.hasFeature('api_access')).toBe(true);
      expect(result.current.hasFeature('priority_support')).toBe(true);
    });

    it('returns correct feature limits', () => {
      const noSubStatus = {
        ...mockStatusInfo,
        hasActiveSubscription: false,
      };

      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: noSubStatus,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionFeatures(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getFeatureLimit('appointments_per_month')).toBe(50);
      expect(result.current.getFeatureLimit('customers')).toBe(100);
      expect(result.current.getFeatureLimit('services')).toBe(5);
    });

    it('returns unlimited limits for subscribers', () => {
      vi.mocked(useSubscriptionStatusInfo).mockReturnValue({
        data: mockStatusInfo,
        isLoading: false,
        error: null,
      } as any);

      const { result } = renderHook(() => useSubscriptionFeatures(), {
        wrapper: createWrapper(),
      });

      expect(result.current.getFeatureLimit('appointments_per_month')).toBeNull();
      expect(result.current.getFeatureLimit('customers')).toBeNull();
      expect(result.current.getFeatureLimit('services')).toBeNull();
    });
  });
});
