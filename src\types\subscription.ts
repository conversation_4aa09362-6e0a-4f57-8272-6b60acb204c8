/**
 * Subscription related types
 */

import { User } from './auth';

/**
 * Subscription plan interface
 */
export interface Subscription {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  description: string;
  price: number; // Price in cents (e.g., 1000 = $10.00)
  duration: number; // Duration in days
  interval: 'monthly' | 'yearly' | 'one-time';
  creditsIncluded: number;
  features: string[]; // Array of features
  isActive: boolean;

  // Relations
  userSubscriptions?: UserSubscription[];
}

/**
 * User subscription instance interface
 */
export interface UserSubscription {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  subscriptionId: string;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate?: string; // null for one-time purchases
  creditsAllocated: number;
  paymentProcessorSubscriptionId?: string;

  // Relations
  user?: User;
  subscription?: Subscription;
}

/**
 * API Response types
 */
export interface SubscriptionPlansResponse {
  success: boolean;
  message: string;
  data: {
    subscriptions: Subscription[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
    };
  };
}

export interface UserSubscriptionResponse {
  success: boolean;
  message: string;
  data: {
    subscription: UserSubscription | null;
    credits: number;
    legacySubscriptionStatus?: string;
    legacySubscriptionPlan?: string;
    datePaid?: string;
  };
}

export interface SubscribeResponse {
  success: boolean;
  message: string;
  data: {
    subscription: UserSubscription;
    updatedCredits: number;
  };
}

/**
 * Request types
 */
export interface SubscribeRequest {
  subscriptionId: string;
  paymentMethodDetails?: {
    paymentMethodId?: string;
  };
}

export interface GetSubscriptionsQuery {
  isActive?: boolean;
  page?: number;
  limit?: number;
}

/**
 * Subscription filters and search
 */
export interface SubscriptionFilters {
  interval?: 'monthly' | 'yearly' | 'one-time';
  minPrice?: number;
  maxPrice?: number;
  minCredits?: number;
  maxCredits?: number;
  isActive?: boolean;
  search?: string;
}

/**
 * Subscription statistics
 */
export interface SubscriptionStats {
  totalPlans: number;
  activePlans: number;
  totalSubscriptions: number;
  activeSubscriptions: number;
  totalRevenue: number;
  averagePrice: number;
  mostPopularPlan?: Subscription;
  recentSubscriptions: UserSubscription[];
}

/**
 * Subscription status display types
 */
export interface SubscriptionStatusInfo {
  hasActiveSubscription: boolean;
  currentPlan?: Subscription;
  creditsRemaining: number;
  expiryDate?: string;
  status: 'active' | 'expired' | 'cancelled' | 'pending' | 'none';
  daysUntilExpiry?: number;
  isExpiringSoon: boolean;
  canUpgrade: boolean;
  canDowngrade: boolean;
}

/**
 * Subscription management actions
 */
export interface SubscriptionAction {
  type: 'subscribe' | 'upgrade' | 'downgrade' | 'cancel' | 'renew';
  subscriptionId?: string;
  targetPlanId?: string;
  reason?: string;
}

/**
 * Subscription notification types
 */
export interface SubscriptionNotification {
  type: 'subscription_success' | 'subscription_error' | 'low_credits' | 'expiry_warning' | 'subscription_cancelled';
  title: string;
  message: string;
  data?: {
    planName?: string;
    creditsRemaining?: number;
    expiryDate?: string;
    error?: string;
  };
}

/**
 * Subscription form data types
 */
export interface SubscriptionFormData {
  selectedPlanId: string;
  paymentMethodId?: string;
  acceptTerms: boolean;
  autoRenew?: boolean;
}

/**
 * Subscription comparison data
 */
export interface SubscriptionComparison {
  plans: Subscription[];
  features: {
    name: string;
    plans: { [planId: string]: boolean | string | number };
  }[];
  recommendations?: {
    planId: string;
    reason: string;
  }[];
}

/**
 * Credit transaction types
 */
export interface CreditTransaction {
  id: string;
  userId: string;
  type: 'allocation' | 'deduction' | 'refund';
  amount: number;
  description: string;
  subscriptionId?: string;
  appointmentId?: string;
  createdAt: string;
}

/**
 * Subscription history types
 */
export interface SubscriptionHistory {
  id: string;
  userId: string;
  action: 'subscribed' | 'upgraded' | 'downgraded' | 'cancelled' | 'renewed' | 'expired';
  fromPlan?: Subscription;
  toPlan?: Subscription;
  amount?: number;
  creditsAllocated?: number;
  reason?: string;
  createdAt: string;
}
