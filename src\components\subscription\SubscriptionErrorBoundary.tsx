import React from 'react';
import { ErrorBoundary, withErrorBoundary } from '../error';
import Button from '../ui/button/Button';
import { ErrorLogger } from '../../lib/error-utils';

interface SubscriptionErrorFallbackProps {
  error?: Error;
  resetError?: () => void;
  context?: string;
}

/**
 * Subscription-specific error fallback component
 */
export const SubscriptionErrorFallback: React.FC<SubscriptionErrorFallbackProps> = ({
  error,
  resetError,
  context = 'subscription',
}) => {
  const handleRetry = () => {
    if (resetError) {
      resetError();
    } else {
      window.location.reload();
    }
  };

  const handleContactSupport = () => {
    // In a real app, this would open a support ticket or chat
    window.open('mailto:<EMAIL>?subject=Subscription Error', '_blank');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl border border-red-200 dark:border-red-800 p-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Subscription Error
        </h3>
        
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          We encountered an issue loading your subscription information. This might be a temporary problem.
        </p>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            onClick={handleRetry}
            size="sm"
            variant="primary"
          >
            Try Again
          </Button>
          <Button
            onClick={handleContactSupport}
            size="sm"
            variant="outline"
          >
            Contact Support
          </Button>
        </div>

        {error && process.env.NODE_ENV === 'development' && (
          <details className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded text-left">
            <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-xs text-red-600 dark:text-red-400 overflow-auto">
              {error.toString()}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
};

/**
 * Subscription Error Boundary wrapper
 */
export const SubscriptionErrorBoundary: React.FC<{
  children: React.ReactNode;
  context?: string;
}> = ({ children, context = 'subscription' }) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    ErrorLogger.log(error, {
      context: `subscription_error_boundary_${context}`,
      errorInfo,
      component: 'SubscriptionErrorBoundary',
    });
  };

  return (
    <ErrorBoundary
      fallback={<SubscriptionErrorFallback context={context} />}
      onError={handleError}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * HOC for wrapping subscription components with error boundary
 */
export function withSubscriptionErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  context?: string
) {
  return withErrorBoundary(
    Component,
    <SubscriptionErrorFallback context={context} />,
    (error, errorInfo) => {
      ErrorLogger.log(error, {
        context: `subscription_hoc_${context || 'unknown'}`,
        errorInfo,
        component: Component.displayName || Component.name,
      });
    }
  );
}

/**
 * Network error component for subscription operations
 */
export const SubscriptionNetworkError: React.FC<{
  onRetry?: () => void;
  operation?: string;
}> = ({ onRetry, operation = 'load subscription data' }) => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-orange-200 dark:border-orange-800 p-6">
    <div className="text-center">
      <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Connection Problem
      </h3>
      
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
        Unable to {operation}. Please check your internet connection and try again.
      </p>

      {onRetry && (
        <Button
          onClick={onRetry}
          size="sm"
          variant="primary"
        >
          Try Again
        </Button>
      )}
    </div>
  </div>
);

/**
 * Subscription access denied error
 */
export const SubscriptionAccessDenied: React.FC<{
  message?: string;
  onUpgrade?: () => void;
}> = ({ 
  message = "You don't have permission to access this subscription feature.",
  onUpgrade 
}) => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl border border-yellow-200 dark:border-yellow-800 p-6">
    <div className="text-center">
      <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      </div>
      
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        Access Restricted
      </h3>
      
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
        {message}
      </p>

      {onUpgrade && (
        <Button
          onClick={onUpgrade}
          size="sm"
          variant="primary"
        >
          Upgrade Plan
        </Button>
      )}
    </div>
  </div>
);

export default SubscriptionErrorBoundary;
