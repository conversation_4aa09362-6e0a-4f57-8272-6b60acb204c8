import toast from 'react-hot-toast';
import { SubscriptionNotification } from '../types';

/**
 * Subscription notification utility functions
 */
export class SubscriptionNotifications {
  
  /**
   * Show subscription success notification
   */
  static showSubscriptionSuccess(planName: string, creditsAdded: number) {
    toast.success(
      `🎉 Welcome to ${planName}! You've received ${creditsAdded.toLocaleString()} credits.`,
      {
        duration: 6000,
        id: 'subscription-success',
        style: {
          background: '#10B981',
          color: '#fff',
        },
      }
    );
  }

  /**
   * Show subscription error notification
   */
  static showSubscriptionError(error: string, planName?: string) {
    const message = planName 
      ? `Failed to subscribe to ${planName}: ${error}`
      : `Subscription failed: ${error}`;
    
    toast.error(message, {
      duration: 8000,
      id: 'subscription-error',
    });
  }

  /**
   * Show low credits warning
   */
  static showLowCreditsWarning(creditsRemaining: number, threshold: number = 10) {
    if (creditsRemaining <= 0) {
      toast.error(
        '⚠️ You have no credits remaining! Upgrade your plan to continue using premium features.',
        {
          duration: 0, // Don't auto-dismiss
          id: 'no-credits-warning',
        }
      );
    } else if (creditsRemaining <= threshold) {
      toast.error(
        `⚠️ Low credits: You have ${creditsRemaining} credits remaining. Consider upgrading your plan.`,
        {
          duration: 8000,
          id: 'low-credits-warning',
        }
      );
    }
  }

  /**
   * Show subscription expiry warning
   */
  static showExpiryWarning(daysUntilExpiry: number, planName?: string) {
    const planText = planName ? ` ${planName}` : '';
    
    if (daysUntilExpiry <= 0) {
      toast.error(
        `🚨 Your${planText} subscription has expired! Renew now to continue using premium features.`,
        {
          duration: 0, // Don't auto-dismiss
          id: 'subscription-expired',
        }
      );
    } else if (daysUntilExpiry === 1) {
      toast.error(
        `⏰ Your${planText} subscription expires tomorrow! Renew now to avoid service interruption.`,
        {
          duration: 10000,
          id: 'expiry-warning-1-day',
        }
      );
    } else if (daysUntilExpiry <= 3) {
      toast.error(
        `⏰ Your${planText} subscription expires in ${daysUntilExpiry} days! Renew now to avoid service interruption.`,
        {
          duration: 8000,
          id: 'expiry-warning-3-days',
        }
      );
    } else if (daysUntilExpiry <= 7) {
      toast.error(
        `📅 Your${planText} subscription expires in ${daysUntilExpiry} days. Consider renewing soon.`,
        {
          duration: 6000,
          id: 'expiry-warning-7-days',
        }
      );
    }
  }

  /**
   * Show subscription cancellation confirmation
   */
  static showCancellationSuccess(planName?: string, endDate?: string) {
    const planText = planName ? ` ${planName}` : '';
    const endText = endDate ? ` You'll retain access until ${new Date(endDate).toLocaleDateString()}.` : '';
    
    toast.success(
      `Your${planText} subscription has been cancelled.${endText}`,
      {
        duration: 8000,
        id: 'subscription-cancelled',
      }
    );
  }

  /**
   * Show subscription upgrade success
   */
  static showUpgradeSuccess(fromPlan: string, toPlan: string, creditsAdded: number) {
    toast.success(
      `🚀 Upgraded from ${fromPlan} to ${toPlan}! You've received ${creditsAdded.toLocaleString()} additional credits.`,
      {
        duration: 6000,
        id: 'subscription-upgraded',
        style: {
          background: '#3B82F6',
          color: '#fff',
        },
      }
    );
  }

  /**
   * Show subscription downgrade notification
   */
  static showDowngradeNotification(fromPlan: string, toPlan: string, effectiveDate?: string) {
    const effectiveText = effectiveDate 
      ? ` Changes will take effect on ${new Date(effectiveDate).toLocaleDateString()}.`
      : ' Changes will take effect at the end of your current billing period.';
    
    toast.success(
      `Downgrade from ${fromPlan} to ${toPlan} has been scheduled.${effectiveText}`,
      {
        duration: 8000,
        id: 'subscription-downgraded',
      }
    );
  }

  /**
   * Show renewal success notification
   */
  static showRenewalSuccess(planName: string, nextBillingDate?: string) {
    const billingText = nextBillingDate 
      ? ` Your next billing date is ${new Date(nextBillingDate).toLocaleDateString()}.`
      : '';
    
    toast.success(
      `✅ ${planName} subscription renewed successfully!${billingText}`,
      {
        duration: 6000,
        id: 'subscription-renewed',
      }
    );
  }

  /**
   * Show payment failure notification
   */
  static showPaymentFailure(planName?: string, retryDate?: string) {
    const planText = planName ? ` for ${planName}` : '';
    const retryText = retryDate 
      ? ` We'll retry on ${new Date(retryDate).toLocaleDateString()}.`
      : ' Please update your payment method.';
    
    toast.error(
      `💳 Payment failed${planText}.${retryText}`,
      {
        duration: 10000,
        id: 'payment-failed',
      }
    );
  }

  /**
   * Show feature access denied notification
   */
  static showFeatureAccessDenied(featureName: string, requiredPlan?: string) {
    const upgradeText = requiredPlan 
      ? ` Upgrade to ${requiredPlan} to access this feature.`
      : ' Upgrade your plan to access this feature.';
    
    toast.error(
      `🔒 ${featureName} requires a subscription.${upgradeText}`,
      {
        duration: 6000,
        id: 'feature-access-denied',
      }
    );
  }

  /**
   * Show insufficient credits notification
   */
  static showInsufficientCredits(action: string, creditsRequired: number, creditsRemaining: number) {
    toast.error(
      `❌ Insufficient credits to ${action}. You need ${creditsRequired} credits but have ${creditsRemaining} remaining.`,
      {
        duration: 6000,
        id: 'insufficient-credits',
      }
    );
  }

  /**
   * Show welcome notification for new subscribers
   */
  static showWelcomeNotification(planName: string, features: string[]) {
    const featureText = features.length > 0 
      ? ` You now have access to: ${features.slice(0, 3).join(', ')}${features.length > 3 ? ' and more!' : '.'}`
      : '';
    
    toast.success(
      `🎊 Welcome to ${planName}!${featureText}`,
      {
        duration: 8000,
        id: 'welcome-notification',
        style: {
          background: '#8B5CF6',
          color: '#fff',
        },
      }
    );
  }

  /**
   * Show trial expiry notification
   */
  static showTrialExpiryNotification(daysRemaining: number) {
    if (daysRemaining <= 0) {
      toast.error(
        '🚨 Your free trial has ended! Choose a plan to continue using premium features.',
        {
          duration: 0, // Don't auto-dismiss
          id: 'trial-expired',
        }
      );
    } else if (daysRemaining === 1) {
      toast.error(
        '⏰ Your free trial ends tomorrow! Choose a plan to continue.',
        {
          duration: 10000,
          id: 'trial-ending-tomorrow',
        }
      );
    } else if (daysRemaining <= 3) {
      toast.error(
        `⏰ Your free trial ends in ${daysRemaining} days! Choose a plan to continue.`,
        {
          duration: 8000,
          id: 'trial-ending-soon',
        }
      );
    }
  }

  /**
   * Dismiss specific notification
   */
  static dismiss(notificationId: string) {
    toast.dismiss(notificationId);
  }

  /**
   * Dismiss all subscription notifications
   */
  static dismissAll() {
    const subscriptionToastIds = [
      'subscription-success',
      'subscription-error',
      'low-credits-warning',
      'no-credits-warning',
      'expiry-warning-1-day',
      'expiry-warning-3-days',
      'expiry-warning-7-days',
      'subscription-expired',
      'subscription-cancelled',
      'subscription-upgraded',
      'subscription-downgraded',
      'subscription-renewed',
      'payment-failed',
      'feature-access-denied',
      'insufficient-credits',
      'welcome-notification',
      'trial-expired',
      'trial-ending-tomorrow',
      'trial-ending-soon',
    ];

    subscriptionToastIds.forEach(id => toast.dismiss(id));
  }

  /**
   * Generic notification handler for subscription events
   */
  static handleNotification(notification: SubscriptionNotification) {
    switch (notification.type) {
      case 'subscription_success':
        this.showSubscriptionSuccess(
          notification.data?.planName || 'Plan',
          notification.data?.creditsRemaining || 0
        );
        break;
      
      case 'subscription_error':
        this.showSubscriptionError(
          notification.data?.error || 'Unknown error',
          notification.data?.planName
        );
        break;
      
      case 'low_credits':
        this.showLowCreditsWarning(notification.data?.creditsRemaining || 0);
        break;
      
      case 'expiry_warning':
        if (notification.data?.expiryDate) {
          const daysUntilExpiry = Math.ceil(
            (new Date(notification.data.expiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
          );
          this.showExpiryWarning(daysUntilExpiry, notification.data?.planName);
        }
        break;
      
      case 'subscription_cancelled':
        this.showCancellationSuccess(
          notification.data?.planName,
          notification.data?.expiryDate
        );
        break;
      
      default:
        toast(notification.message, {
          duration: 5000,
        });
    }
  }
}
