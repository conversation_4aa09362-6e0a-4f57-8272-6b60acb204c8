import React from 'react';
import { Subscription } from '../../types';

interface SubscriptionPlanListProps {
  plans: Subscription[];
  currentPlanId?: string;
  onSelectPlan: (planId: string) => void;
  isLoading?: boolean;
  className?: string;
}

export default function SubscriptionPlanList({
  plans,
  currentPlanId,
  onSelectPlan,
  isLoading = false,
  className = '',
}: SubscriptionPlanListProps) {
  
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100);
  };

  const formatInterval = (interval: string): string => {
    switch (interval) {
      case 'monthly':
        return '/mo';
      case 'yearly':
        return '/yr';
      case 'one-time':
        return '';
      default:
        return `/${interval}`;
    }
  };

  const getPlanColor = (plan: Subscription): string => {
    switch (plan.interval) {
      case 'monthly':
        return 'text-blue-500';
      case 'yearly':
        return 'text-green-500';
      case 'one-time':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };

  const getFeatureIcon = (plan: Subscription): string => {
    if (plan.interval === 'one-time') {
      return '💳'; // Credit card for one-time purchases
    }
    return '👥'; // Teams icon for subscriptions
  };

  const getFeatureText = (plan: Subscription): string => {
    if (plan.interval === 'one-time') {
      return 'Credits';
    }
    return 'Teams';
  };

  const isCurrentPlan = (planId: string): boolean => {
    return currentPlanId === planId;
  };

  const isFree = (plan: Subscription): boolean => {
    return plan.price === 0;
  };

  // Group plans by type
  const recurringPlans = plans.filter(plan => 
    plan.interval === 'monthly' || plan.interval === 'yearly'
  );
  const oneTimePlans = plans.filter(plan => 
    plan.interval === 'one-time'
  );

  const renderPlan = (plan: Subscription) => (
    <div
      key={plan.id}
      className={`
        relative border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-md
        ${isCurrentPlan(plan.id) 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/10' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }
        ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
      `}
      onClick={() => !isLoading && !isCurrentPlan(plan.id) && onSelectPlan(plan.id)}
      role="button"
      tabIndex={0}
      aria-label={`Select ${plan.name} plan`}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && !isLoading && !isCurrentPlan(plan.id)) {
          e.preventDefault();
          onSelectPlan(plan.id);
        }
      }}
    >
      {/* Current Plan Badge */}
      {isCurrentPlan(plan.id) && (
        <div className="absolute top-3 right-3">
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            ✓ Current
          </span>
        </div>
      )}

      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* Plan Name and Price */}
          <div className="flex items-center justify-between mb-2">
            <h3 className={`text-lg font-semibold ${getPlanColor(plan)}`}>
              {plan.name}
            </h3>
            <div className="text-right">
              {isFree(plan) ? (
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  Free
                </span>
              ) : (
                <span className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatPrice(plan.price)}{formatInterval(plan.interval)}
                </span>
              )}
            </div>
          </div>

          {/* Credits and Features */}
          <div className="flex items-center gap-4 mb-2">
            <div className="flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white">
              <span>{plan.creditsIncluded.toLocaleString()}</span>
              <span className="text-gray-500 dark:text-gray-400">
                {plan.interval === 'one-time' ? 'credits total' : 'credits/mo'}
              </span>
            </div>
            
            <div className="flex items-center gap-1 text-sm text-teal-600 dark:text-teal-400">
              <span>{getFeatureIcon(plan)}</span>
              <span>{getFeatureText(plan)}</span>
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {plan.description}
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Recurring Plans Section */}
      {recurringPlans.length > 0 && (
        <div>
          {recurringPlans.map(renderPlan)}
        </div>
      )}

      {/* One-Time Plans Section */}
      {oneTimePlans.length > 0 && (
        <div>
          {recurringPlans.length > 0 && (
            <div className="flex items-center justify-center py-4">
              <div className="flex items-center gap-3">
                <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1 w-16"></div>
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Credit Packs
                </span>
                <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1 w-16"></div>
              </div>
            </div>
          )}
          {oneTimePlans.map(renderPlan)}
        </div>
      )}

      {/* Enterprise Section */}
      <div className="flex items-center justify-center py-4">
        <div className="flex items-center gap-3">
          <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1 w-16"></div>
          <span className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
            Enterprise Solutions
          </span>
          <div className="h-px bg-gray-300 dark:bg-gray-600 flex-1 w-16"></div>
        </div>
      </div>

      {/* Enterprise Plan */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Enterprise Plan
            </h3>
            <div className="flex items-center gap-4 mb-2">
              <div className="flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white">
                <span>Custom credit limits</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-teal-600 dark:text-teal-400">
                <span>👥</span>
                <span>Teams</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Enterprise plans including SSO, dedicated support, and volume discounts.
            </p>
          </div>
          <div className="ml-4">
            <button
              className="px-4 py-2 bg-gray-900 dark:bg-white text-white dark:text-gray-900 text-sm font-medium rounded-lg hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors"
              onClick={() => {
                // Handle contact us action
                window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
              }}
            >
              Contact Us
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
