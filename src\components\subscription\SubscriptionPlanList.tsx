import React from 'react';
import { Subscription } from '../../types';

interface SubscriptionPlanListProps {
  plans: Subscription[];
  currentPlanId?: string;
  onSelectPlan: (planId: string) => void;
  isLoading?: boolean;
  className?: string;
}

export default function SubscriptionPlanList({
  plans,
  currentPlanId,
  onSelectPlan,
  isLoading = false,
  className = '',
}: SubscriptionPlanListProps) {

  const formatPrice = (price: number): string => {
    if (price === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(price / 100);
  };

  const formatInterval = (interval: string): string => {
    switch (interval) {
      case 'monthly':
        return '/mo';
      case 'yearly':
        return '/yr';
      case 'one-time':
        return '';
      default:
        return `/${interval}`;
    }
  };

  const getPlanColor = (plan: Subscription): string => {
    if (plan.price === 0) return 'text-blue-500'; // Free plan
    switch (plan.interval) {
      case 'monthly':
        return 'text-blue-500';
      case 'yearly':
        return 'text-green-500';
      case 'one-time':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };

  const getPriceColor = (plan: Subscription): string => {
    if (plan.price === 0) return 'text-blue-500'; // Free plan
    switch (plan.interval) {
      case 'monthly':
        return 'text-blue-500';
      case 'yearly':
        return 'text-green-500';
      case 'one-time':
        return 'text-orange-500';
      default:
        return 'text-gray-900 dark:text-white';
    }
  };

  const isCurrentPlan = (planId: string): boolean => {
    return currentPlanId === planId;
  };

  const isFree = (plan: Subscription): boolean => {
    return plan.price === 0;
  };

  const renderPlan = (plan: Subscription) => (
    <div
      key={plan.id}
      className={`
        relative border rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-sm
        ${isCurrentPlan(plan.id)
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/10'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }
        ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
        bg-white dark:bg-gray-800
      `}
      onClick={() => !isLoading && !isCurrentPlan(plan.id) && onSelectPlan(plan.id)}
      role="button"
      tabIndex={0}
      aria-label={`Select ${plan.name} plan`}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && !isLoading && !isCurrentPlan(plan.id)) {
          e.preventDefault();
          onSelectPlan(plan.id);
        }
      }}
    >
      {/* Header with Plan Name and Price */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <h3 className={`text-lg font-semibold ${getPlanColor(plan)}`}>
            {plan.name}
          </h3>
          {isCurrentPlan(plan.id) && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              ✓ Current
            </span>
          )}
        </div>
        <div className={`text-2xl font-bold ${getPriceColor(plan)}`}>
          {formatPrice(plan.price)}{!isFree(plan) && formatInterval(plan.interval)}
        </div>
      </div>

      {/* Credits/Usage Info */}
      <div className="flex items-center gap-4 mb-3">
        <div className="flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white">
          <span>{plan.creditsIncluded.toLocaleString()}</span>
          <span className="text-gray-500 dark:text-gray-400">
            {plan.interval === 'one-time' ? 'credits total' : 'user messages/mo'}
          </span>
        </div>

        {/* AI Training indicator for free plans */}
        {isFree(plan) && (
          <div className="flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            <span>AI Training</span>
          </div>
        )}

        {/* Teams indicator for paid plans */}
        {!isFree(plan) && (
          <div className="flex items-center gap-1 text-sm text-teal-600 dark:text-teal-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span>Teams</span>
          </div>
        )}
      </div>

      {/* Description */}
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {plan.description}
      </p>
    </div>
  );

  // Group plans by type - separate recurring from one-time
  const recurringPlans = plans.filter(plan =>
    plan.interval === 'monthly' || plan.interval === 'yearly'
  );
  const oneTimePlans = plans.filter(plan =>
    plan.interval === 'one-time'
  );

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Main subscription plans */}
      {recurringPlans.map(renderPlan)}

      {/* One-time credit packs */}
      {oneTimePlans.map(renderPlan)}

      {/* Enterprise Solutions Section */}
      <div className="flex items-center justify-center py-4">
        <span className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
          Enterprise Solutions
        </span>
      </div>

      {/* Enterprise Plan */}
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Enterprise Plan
            </h3>
            <div className="flex items-center gap-4 mb-3">
              <div className="flex items-center gap-1 text-sm font-medium text-gray-900 dark:text-white">
                <span>Bespoke user messages limits</span>
              </div>
              <div className="flex items-center gap-1 text-sm text-teal-600 dark:text-teal-400">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>Teams</span>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Enterprise plans including SSO, OIDC, SCIM, Slack integration, dedicated support, and volume discounts.
            </p>
          </div>
          <div className="ml-4">
            <button
              className="px-4 py-2 bg-gray-900 dark:bg-white text-white dark:text-gray-900 text-sm font-medium rounded-lg hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors"
              onClick={() => {
                // Handle contact us action
                window.open('mailto:<EMAIL>?subject=Enterprise Plan Inquiry', '_blank');
              }}
            >
              Contact Us
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
