import React, { useState } from 'react';
import Button from '../ui/button/Button';
import ConfirmationDialog from '../ui/confirmation/ConfirmationDialog';
import { Modal } from '../ui/modal';
import { useUserSubscription, useSubscribeToplan } from '../../hooks/useSubscriptions';
import { useConfirmation } from '../../hooks/useConfirmation';
import { SubscriptionPlanSelectionFlow } from './';
import toast from 'react-hot-toast';

interface SubscriptionManagementActionsProps {
  className?: string;
}

export default function SubscriptionManagementActions({
  className = '',
}: SubscriptionManagementActionsProps) {
  const [showUpgradeFlow, setShowUpgradeFlow] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [isProcessingCancellation, setIsProcessingCancellation] = useState(false);

  const { data: userSubscriptionData, isLoading } = useUserSubscription();
  const subscribeToplanMutation = useSubscribeToplan();
  const confirmation = useConfirmation();

  const userSubscription = userSubscriptionData?.data.subscription;
  const creditsRemaining = userSubscriptionData?.data.credits || 0;

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleUpgrade = () => {
    setShowUpgradeFlow(true);
  };

  const handleDowngrade = async () => {
    const confirmed = await confirmation.confirm({
      title: 'Downgrade Subscription',
      message: 'Are you sure you want to downgrade your subscription? This action will take effect at the end of your current billing period. You may lose access to some premium features.',
      confirmText: 'Downgrade',
      cancelText: 'Keep Current Plan',
      variant: 'warning'
    });

    if (confirmed) {
      try {
        // In a real implementation, this would call a downgrade API
        toast.success('Downgrade request submitted. Changes will take effect at the end of your billing period.');
        confirmation.close();
      } catch (error) {
        toast.error('Failed to process downgrade request. Please try again.');
        confirmation.close();
      }
    }
  };

  const handleCancelSubscription = () => {
    setShowCancelConfirmation(true);
  };

  const handleConfirmCancellation = async () => {
    setIsProcessingCancellation(true);
    
    try {
      // In a real implementation, this would call a cancellation API
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      toast.success('Subscription cancelled successfully. You will retain access until the end of your billing period.');
      setShowCancelConfirmation(false);
      
      // Refresh subscription data
      // This would typically be handled by React Query invalidation
      
    } catch (error) {
      toast.error('Failed to cancel subscription. Please try again or contact support.');
    } finally {
      setIsProcessingCancellation(false);
    }
  };

  const handleRenewSubscription = async () => {
    if (!userSubscription?.subscription) return;

    const confirmed = await confirmation.confirm({
      title: 'Renew Subscription',
      message: `Are you sure you want to renew your ${userSubscription.subscription.name} subscription? This will extend your subscription for another ${userSubscription.subscription.interval}.`,
      confirmText: 'Renew Now',
      cancelText: 'Cancel',
      variant: 'info'
    });

    if (confirmed) {
      try {
        await subscribeToplanMutation.mutateAsync({
          subscriptionId: userSubscription.subscription.id,
        });
        confirmation.close();
      } catch (error) {
        confirmation.close();
        // Error handled by mutation
      }
    }
  };

  const getDaysUntilExpiry = (endDate?: string): number | null => {
    if (!endDate) return null;
    const now = new Date();
    const expiry = new Date(endDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilExpiry = getDaysUntilExpiry(userSubscription?.endDate);
  const isExpired = userSubscription?.status === 'expired';
  const isActive = userSubscription?.status === 'active';
  const isCancelled = userSubscription?.status === 'cancelled';

  if (isLoading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!userSubscription) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Subscription Actions
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          You don't have an active subscription. Choose a plan to get started.
        </p>
        <Button onClick={handleUpgrade} className="w-full">
          Choose a Plan
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className={`bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Subscription Actions
        </h3>

        <div className="space-y-4">
          {/* Upgrade/Downgrade Actions */}
          {isActive && (
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={handleUpgrade}
                variant="primary"
                size="sm"
                className="flex-1"
              >
                Upgrade Plan
              </Button>
              <Button
                onClick={handleDowngrade}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                Downgrade Plan
              </Button>
            </div>
          )}

          {/* Renewal Action for Expired/Expiring */}
          {(isExpired || (daysUntilExpiry && daysUntilExpiry <= 7)) && (
            <Button
              onClick={handleRenewSubscription}
              variant="primary"
              size="sm"
              className="w-full"
              disabled={subscribeToplanMutation.isPending}
            >
              {subscribeToplanMutation.isPending ? 'Processing...' : 'Renew Subscription'}
            </Button>
          )}

          {/* Reactivate for Cancelled */}
          {isCancelled && (
            <Button
              onClick={handleRenewSubscription}
              variant="primary"
              size="sm"
              className="w-full"
              disabled={subscribeToplanMutation.isPending}
            >
              {subscribeToplanMutation.isPending ? 'Processing...' : 'Reactivate Subscription'}
            </Button>
          )}

          {/* Cancellation Action */}
          {isActive && !isCancelled && (
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                onClick={handleCancelSubscription}
                variant="outline"
                size="sm"
                className="w-full text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 border-red-200 hover:border-red-300 dark:border-red-800 dark:hover:border-red-700"
              >
                Cancel Subscription
              </Button>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                You'll retain access until {userSubscription.endDate ? formatDate(userSubscription.endDate) : 'the end of your billing period'}
              </p>
            </div>
          )}

          {/* Status Information */}
          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
              {isActive && daysUntilExpiry && daysUntilExpiry > 0 && (
                <p>Your subscription renews in {daysUntilExpiry} days</p>
              )}
              {isExpired && (
                <p className="text-red-600 dark:text-red-400">Your subscription has expired</p>
              )}
              {isCancelled && (
                <p className="text-orange-600 dark:text-orange-400">
                  Your subscription is cancelled and will end on {userSubscription.endDate ? formatDate(userSubscription.endDate) : 'the billing date'}
                </p>
              )}
              <p>Need help? <a href="/support" className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300">Contact support</a></p>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Flow Modal */}
      <SubscriptionPlanSelectionFlow
        isOpen={showUpgradeFlow}
        onClose={() => setShowUpgradeFlow(false)}
        onSuccess={() => {
          setShowUpgradeFlow(false);
          toast.success('Subscription upgraded successfully!');
        }}
      />

      {/* Cancellation Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showCancelConfirmation}
        onClose={() => setShowCancelConfirmation(false)}
        onConfirm={handleConfirmCancellation}
        title="Cancel Subscription"
        message={`Are you sure you want to cancel your ${userSubscription.subscription?.name} subscription? You will lose access to premium features at the end of your billing period. This action cannot be undone.`}
        confirmText="Yes, Cancel Subscription"
        cancelText="Keep Subscription"
        isLoading={isProcessingCancellation}
        variant="danger"
      />

      {/* General Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmation.isOpen}
        onClose={confirmation.cancel}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        variant={confirmation.variant}
        isLoading={confirmation.isLoading}
      />
    </>
  );
}
