import React from 'react';
import Button from '../ui/button/Button';
import { Subscription } from '../../types';

interface SubscriptionPlanCardProps {
  plan: Subscription;
  isCurrentPlan?: boolean;
  isPopular?: boolean;
  onSubscribe: (planId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

export default function SubscriptionPlanCard({
  plan,
  isCurrentPlan = false,
  isPopular = false,
  onSubscribe,
  isLoading = false,
  disabled = false,
  className = '',
}: SubscriptionPlanCardProps) {
  
  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100); // Convert from cents to dollars
  };

  const formatInterval = (interval: string): string => {
    switch (interval) {
      case 'monthly':
        return '/month';
      case 'yearly':
        return '/year';
      case 'one-time':
        return '';
      default:
        return `/${interval}`;
    }
  };

  const formatDuration = (duration: number, interval: string): string => {
    if (interval === 'one-time') {
      return 'One-time purchase';
    }
    if (duration === 30) {
      return '30 days';
    }
    if (duration === 365) {
      return '1 year';
    }
    return `${duration} days`;
  };

  const getIntervalColor = (interval: string): string => {
    switch (interval) {
      case 'monthly':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'yearly':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'one-time':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';

  const getIntervalDisplayText = (interval: string): string => {
    switch (interval) {
      case 'monthly':
        return 'Monthly';
      case 'yearly':
        return 'Yearly';
      case 'one-time':
        return 'One-time';
      default:
        return interval;
    }
  };

  const handleSubscribe = () => {
    if (!disabled && !isLoading && !isCurrentPlan) {
      onSubscribe(plan.id);
    }
  };

  const getButtonText = (): string => {
    if (isCurrentPlan) return 'Current Plan';
    if (isLoading) return 'Processing...';
    return plan.interval === 'one-time' ? 'Purchase' : 'Subscribe';
  };

  const getButtonVariant = (): 'primary' | 'outline' => {
    if (isCurrentPlan) return 'outline';
    return 'primary';
  };

  return (
    <div
      className={`
        relative bg-white dark:bg-gray-800 rounded-2xl border-2 transition-all duration-200 hover:shadow-lg focus-within:ring-2 focus-within:ring-brand-500 focus-within:ring-opacity-50
        ${isPopular
          ? 'border-brand-500 shadow-lg'
          : 'border-gray-200 dark:border-gray-700 hover:border-brand-300 dark:hover:border-brand-600'
        }
        ${isCurrentPlan ? 'ring-2 ring-brand-500 ring-opacity-50' : ''}
        ${className}
      `}
      role="article"
      aria-labelledby={`plan-${plan.id}-title`}
      aria-describedby={`plan-${plan.id}-description`}
    >
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <span className="bg-brand-500 text-white px-4 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 right-4">
          <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            Current
          </span>
        </div>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-2 flex-wrap">
            <h3
              id={`plan-${plan.id}-title`}
              className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white"
            >
              {plan.name}
            </h3>
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getIntervalColor(plan.interval)}`}
              aria-label={`Billing interval: ${getIntervalDisplayText(plan.interval)}`}
            >
              {getIntervalDisplayText(plan.interval)}
            </span>
          </div>
          
          <p
            id={`plan-${plan.id}-description`}
            className="text-sm text-gray-500 dark:text-gray-400 mb-4 px-2"
          >
            {plan.description}
          </p>

          {/* Price */}
          <div className="mb-2">
            <span
              className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white"
              aria-label={`Price: ${formatPrice(plan.price)}`}
            >
              {formatPrice(plan.price)}
            </span>
            <span className="text-base sm:text-lg text-gray-500 dark:text-gray-400 ml-1">
              {formatInterval(plan.interval)}
            </span>
          </div>

          {/* Duration */}
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {formatDuration(plan.duration, plan.interval)}
          </p>
        </div>

        {/* Credits */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 sm:p-4 mb-6">
          <div className="text-center">
            <div
              className="text-xl sm:text-2xl font-bold text-brand-600 dark:text-brand-400"
              aria-label={`${plan.creditsIncluded.toLocaleString()} credits ${plan.interval === 'one-time' ? 'total' : 'included'}`}
            >
              {plan.creditsIncluded.toLocaleString()}
            </div>
            <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
              {plan.interval === 'one-time' ? 'Credits Total' : 'Credits Included'}
            </div>
            {plan.interval !== 'one-time' && (
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Renewed {plan.interval === 'monthly' ? 'monthly' : 'yearly'}
              </div>
            )}
          </div>
        </div>

        {/* Features */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 px-1">
            Features included:
          </h4>
          <ul className="space-y-2" role="list" aria-label="Plan features">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-start" role="listitem">
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 text-brand-500 mr-2 mt-0.5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                  {feature}
                </span>
              </li>
            ))}
          </ul>
        </div>

        {/* Subscribe Button */}
        <Button
          variant={getButtonVariant()}
          size="md"
          onClick={handleSubscribe}
          disabled={disabled || isLoading || isCurrentPlan}
          className="w-full"
          aria-label={isCurrentPlan ? `Current plan: ${plan.name}` : `${plan.interval === 'one-time' ? 'Purchase' : 'Subscribe to'} ${plan.name}`}
          aria-describedby={`plan-${plan.id}-description`}
        >
          {getButtonText()}
        </Button>

        {/* Additional Info */}
        {plan.interval !== 'one-time' && (
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-3">
            Cancel anytime. No hidden fees.
          </p>
        )}
      </div>
    </div>
  );
}
