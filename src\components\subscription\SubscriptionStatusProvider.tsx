import React, { createContext, useContext, useEffect } from 'react';
import { useSubscriptionMonitoring } from '../../hooks/useSubscriptionMonitoring';

interface SubscriptionStatusContextType {
  hasActiveSubscription: boolean;
  creditsRemaining: number;
  isExpiringSoon: boolean;
  isLowOnCredits: boolean;
  canUpgrade: boolean;
  canDowngrade: boolean;
  isLoading: boolean;
}

const SubscriptionStatusContext = createContext<SubscriptionStatusContextType | undefined>(undefined);

interface SubscriptionStatusProviderProps {
  children: React.ReactNode;
  enableMonitoring?: boolean;
}

export const SubscriptionStatusProvider: React.FC<SubscriptionStatusProviderProps> = ({
  children,
  enableMonitoring = true,
}) => {
  const monitoring = useSubscriptionMonitoring({
    enableLowCreditWarnings: enableMonitoring,
    enableExpiryWarnings: enableMonitoring,
    lowCreditThreshold: 10,
    expiryWarningDays: 7,
  });

  const value: SubscriptionStatusContextType = {
    hasActiveSubscription: monitoring.hasActiveSubscription,
    creditsRemaining: monitoring.creditBalance || 0,
    isExpiringSoon: monitoring.isExpiringSoon,
    isLowOnCredits: monitoring.isLowOnCredits,
    canUpgrade: monitoring.canUpgrade,
    canDowngrade: monitoring.canDowngrade,
    isLoading: monitoring.isLoading,
  };

  return (
    <SubscriptionStatusContext.Provider value={value}>
      {children}
    </SubscriptionStatusContext.Provider>
  );
};

export const useSubscriptionStatus = (): SubscriptionStatusContextType => {
  const context = useContext(SubscriptionStatusContext);
  if (context === undefined) {
    throw new Error('useSubscriptionStatus must be used within a SubscriptionStatusProvider');
  }
  return context;
};
