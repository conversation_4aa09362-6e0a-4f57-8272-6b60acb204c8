import React, { useState } from 'react';
import SubscriptionPlanList from './SubscriptionPlanList';
import { Subscription } from '../../types';
import Button from '../ui/button/Button';

// Sample data that matches the design from your example
const samplePlans: Subscription[] = [
  {
    id: 'community-plan',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    name: 'Community Plan',
    description: 'Community Plan with 50 user messages per month.',
    price: 0, // Free
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 50,
    features: [
      'AI Training',
      'Community Support',
      'Basic Analytics',
      'Up to 5 appointments/month',
      'Email notifications'
    ],
    isActive: true,
  },
  {
    id: 'developer-plan',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    name: 'Developer Plan',
    description: 'Developer Plan with 600 user messages per month.',
    price: 5000, // $50.00
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 600,
    features: [
      'Teams collaboration',
      'Priority Support',
      'API Access',
      'Advanced Analytics',
      'Unlimited appointments',
      'SMS notifications',
      'Custom branding',
      'Queue management'
    ],
    isActive: true,
  },
  {
    id: 'pro-plan',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    name: 'Pro Plan',
    description: 'Pro Plan with 1500 user messages per month.',
    price: 10000, // $100.00
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 1500,
    features: [
      'Everything in Developer',
      'Advanced team management',
      'Custom integrations',
      'White-label solution',
      'Advanced reporting',
      'Multi-location support',
      'Priority phone support',
      'Custom workflows'
    ],
    isActive: true,
  },
  {
    id: 'max-plan',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    name: 'Max Plan',
    description: 'Max Plan with 4500 user messages per month.',
    price: 25000, // $250.00
    duration: 30,
    interval: 'monthly',
    creditsIncluded: 4500,
    features: [
      'Everything in Pro',
      'Unlimited team members',
      'Enterprise integrations',
      'Dedicated account manager',
      'Custom development',
      'SLA guarantees',
      '24/7 phone support',
      'On-premise deployment',
      'Advanced security features'
    ],
    isActive: true,
  },
];

interface SubscriptionPlanListDemoProps {
  className?: string;
}

export default function SubscriptionPlanListDemo({ className = '' }: SubscriptionPlanListDemoProps) {
  const [currentPlanId, setCurrentPlanId] = useState<string>('community-plan');
  const [isLoading, setIsLoading] = useState(false);

  const handleSelectPlan = (planId: string) => {
    setIsLoading(true);
    console.log('Selected plan:', planId);
    
    // Simulate API call
    setTimeout(() => {
      setCurrentPlanId(planId);
      setIsLoading(false);
      alert(`Successfully subscribed to ${samplePlans.find(p => p.id === planId)?.name}!`);
    }, 1500);
  };

  return (
    <div className={`max-w-2xl mx-auto p-6 ${className}`}>
      <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="p-6 pb-4">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
            Choose a Plan
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Select the plan that best fits your needs. 
            <button 
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 ml-1"
              onClick={() => alert('View detailed plan information')}
            >
              View detailed plan information
            </button>
          </p>
        </div>

        {/* Content */}
        <div className="px-6 pb-4 max-h-[70vh] overflow-y-auto">
          <SubscriptionPlanList
            plans={samplePlans}
            currentPlanId={currentPlanId}
            onSelectPlan={handleSelectPlan}
            isLoading={isLoading}
            popularPlanId="developer-plan"
          />
        </div>

        {/* Footer */}
        <div className="p-6 pt-4 flex justify-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => alert('Cancel clicked')}
            disabled={isLoading}
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
