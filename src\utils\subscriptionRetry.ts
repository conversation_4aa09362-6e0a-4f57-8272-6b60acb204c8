import { <PERSON>rror<PERSON>ogger } from '../lib/error-utils';
import { SubscriptionNotifications } from './subscriptionNotifications';

/**
 * Retry configuration interface
 */
interface RetryConfig {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  retryCondition?: (error: any) => boolean;
  onRetry?: (attempt: number, error: any) => void;
}

/**
 * Default retry configuration for subscription operations
 */
const DEFAULT_RETRY_CONFIG: Required<RetryConfig> = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
  retryCondition: (error: any) => {
    // Retry on network errors, 5xx server errors, and timeout errors
    if (!error.status) return true; // Network error
    if (error.status >= 500) return true; // Server error
    if (error.status === 408) return true; // Request timeout
    if (error.status === 429) return true; // Rate limit (with backoff)
    return false;
  },
  onRetry: () => {}, // No-op by default
};

/**
 * Subscription-specific retry utility
 */
export class SubscriptionRetry {
  /**
   * Execute a function with retry logic
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: RetryConfig = {},
    context?: string
  ): Promise<T> {
    const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
    let lastError: any;

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Log the error
        ErrorLogger.log(error, {
          context: `subscription_retry_${context || 'unknown'}`,
          attempt,
          maxAttempts: finalConfig.maxAttempts,
        });

        // Check if we should retry
        if (attempt === finalConfig.maxAttempts || !finalConfig.retryCondition(error)) {
          break;
        }

        // Call retry callback
        finalConfig.onRetry(attempt, error);

        // Calculate delay with exponential backoff
        const delay = Math.min(
          finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1),
          finalConfig.maxDelay
        );

        // Add jitter to prevent thundering herd
        const jitteredDelay = delay + Math.random() * 1000;

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, jitteredDelay));
      }
    }

    // All retries failed, throw the last error
    throw lastError;
  }

  /**
   * Retry subscription plan fetching
   */
  static async retryFetchPlans<T>(
    operation: () => Promise<T>,
    showNotifications = true
  ): Promise<T> {
    return this.withRetry(
      operation,
      {
        maxAttempts: 3,
        onRetry: (attempt, error) => {
          if (showNotifications && attempt === 1) {
            SubscriptionNotifications.dismiss('subscription-error');
            // Don't show notification on first retry, only if it keeps failing
          }
        },
      },
      'fetch_plans'
    );
  }

  /**
   * Retry subscription purchase operation
   */
  static async retrySubscription<T>(
    operation: () => Promise<T>,
    planName?: string
  ): Promise<T> {
    return this.withRetry(
      operation,
      {
        maxAttempts: 2, // Less retries for payment operations
        retryCondition: (error: any) => {
          // Don't retry on client errors (4xx) except timeout and rate limit
          if (error.status >= 400 && error.status < 500) {
            return error.status === 408 || error.status === 429;
          }
          return DEFAULT_RETRY_CONFIG.retryCondition(error);
        },
        onRetry: (attempt, error) => {
          if (attempt === 1) {
            SubscriptionNotifications.dismiss('subscription-error');
            // Show a "retrying" message for subscription operations
            if (planName) {
              SubscriptionNotifications.showSubscriptionError(
                `Retrying subscription to ${planName}...`,
                planName
              );
            }
          }
        },
      },
      'subscription_purchase'
    );
  }

  /**
   * Retry user subscription status fetching
   */
  static async retryUserSubscription<T>(
    operation: () => Promise<T>
  ): Promise<T> {
    return this.withRetry(
      operation,
      {
        maxAttempts: 2,
        baseDelay: 500, // Shorter delay for status checks
        onRetry: (attempt, error) => {
          // Don't show notifications for status check retries
          // as they happen frequently in the background
        },
      },
      'user_subscription_status'
    );
  }

  /**
   * Retry subscription management actions (cancel, upgrade, etc.)
   */
  static async retryManagementAction<T>(
    operation: () => Promise<T>,
    actionName: string
  ): Promise<T> {
    return this.withRetry(
      operation,
      {
        maxAttempts: 2,
        retryCondition: (error: any) => {
          // Be more conservative with management actions
          // Only retry on clear network/server errors
          if (!error.status) return true; // Network error
          if (error.status >= 500) return true; // Server error
          return false;
        },
        onRetry: (attempt, error) => {
          if (attempt === 1) {
            SubscriptionNotifications.dismiss('subscription-error');
          }
        },
      },
      `management_${actionName}`
    );
  }

  /**
   * Create a retry wrapper for React Query operations
   */
  static createQueryRetry(context: string) {
    return {
      retry: (failureCount: number, error: any) => {
        // Use React Query's built-in retry logic but with our conditions
        if (failureCount >= 3) return false;
        return DEFAULT_RETRY_CONFIG.retryCondition(error);
      },
      retryDelay: (attemptIndex: number) => {
        // Exponential backoff with jitter
        const delay = Math.min(1000 * Math.pow(2, attemptIndex), 10000);
        return delay + Math.random() * 1000;
      },
      onError: (error: any) => {
        ErrorLogger.log(error, {
          context: `subscription_query_${context}`,
          queryRetry: true,
        });
      },
    };
  }

  /**
   * Create a retry wrapper for React Query mutations
   */
  static createMutationRetry(context: string) {
    return {
      retry: (failureCount: number, error: any) => {
        // More conservative retry for mutations
        if (failureCount >= 2) return false;
        return DEFAULT_RETRY_CONFIG.retryCondition(error);
      },
      retryDelay: (attemptIndex: number) => {
        return Math.min(2000 * Math.pow(2, attemptIndex), 8000);
      },
      onError: (error: any) => {
        ErrorLogger.log(error, {
          context: `subscription_mutation_${context}`,
          mutationRetry: true,
        });
      },
    };
  }

  /**
   * Check if an error is retryable
   */
  static isRetryableError(error: any): boolean {
    return DEFAULT_RETRY_CONFIG.retryCondition(error);
  }

  /**
   * Get user-friendly error message for subscription errors
   */
  static getErrorMessage(error: any, operation: string): string {
    if (!error.status) {
      return `Unable to ${operation} due to network issues. Please check your connection and try again.`;
    }

    if (error.status >= 500) {
      return `Server error while trying to ${operation}. Please try again in a moment.`;
    }

    if (error.status === 429) {
      return `Too many requests. Please wait a moment before trying to ${operation} again.`;
    }

    if (error.status === 408) {
      return `Request timed out while trying to ${operation}. Please try again.`;
    }

    // Use the error message from the API if available
    return error.message || `Failed to ${operation}. Please try again.`;
  }

  /**
   * Handle subscription operation with comprehensive error handling
   */
  static async handleSubscriptionOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    config?: RetryConfig
  ): Promise<T> {
    try {
      return await this.withRetry(operation, config, operationName);
    } catch (error) {
      const message = this.getErrorMessage(error, operationName);
      SubscriptionNotifications.showSubscriptionError(message);
      throw error;
    }
  }
}
