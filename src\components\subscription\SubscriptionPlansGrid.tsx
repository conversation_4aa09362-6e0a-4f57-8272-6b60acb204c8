import React, { useState, useMemo } from 'react';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import { Subscription, SubscriptionFilters } from '../../types';
import { useFilteredSubscriptionPlans } from '../../hooks/useSubscriptions';
import { SubscriptionPlansGridSkeleton, SubscriptionNetworkError } from './';

interface SubscriptionPlansGridProps {
  onSubscribe: (planId: string) => void;
  currentPlanId?: string;
  isLoading?: boolean;
  className?: string;
  showFilters?: boolean;
  popularPlanId?: string;
}

export default function SubscriptionPlansGrid({
  onSubscribe,
  currentPlanId,
  isLoading: externalLoading = false,
  className = '',
  showFilters = true,
  popularPlanId,
}: SubscriptionPlansGridProps) {
  const [filters, setFilters] = useState<SubscriptionFilters>({
    isActive: true,
  });

  const { 
    data: plans = [], 
    isLoading: plansLoading, 
    error 
  } = useFilteredSubscriptionPlans(filters);

  const isLoading = plansLoading || externalLoading;

  const handleFilterChange = (newFilters: Partial<SubscriptionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({ isActive: true });
  };

  const hasActiveFilters = Object.keys(filters).some(key =>
    key !== 'isActive' && filters[key as keyof SubscriptionFilters] !== undefined
  );

  // Group plans by billing type
  const { recurringPlans, oneTimePlans } = useMemo(() => {
    const recurring = plans.filter(plan =>
      plan.interval === 'monthly' || plan.interval === 'yearly'
    ).sort((a, b) => a.price - b.price);

    const oneTime = plans.filter(plan =>
      plan.interval === 'one-time'
    ).sort((a, b) => a.price - b.price);

    return {
      recurringPlans: recurring,
      oneTimePlans: oneTime
    };
  }, [plans]);

  // Loading skeleton
  if (isLoading) {
    return (
      <div className={className}>
        <SubscriptionPlansGridSkeleton count={3} />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={className}>
        <SubscriptionNetworkError
          onRetry={() => window.location.reload()}
          operation="load subscription plans"
        />
      </div>
    );
  }

  // Empty state
  if (!plans || plans.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No subscription plans available
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          {hasActiveFilters 
            ? 'No plans match your current filters. Try adjusting your search criteria.'
            : 'There are currently no subscription plans available.'
          }
        </p>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 text-sm font-medium"
          >
            Clear filters
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Filters */}
      {showFilters && (
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            {/* Interval Filter */}
            <div
              className="flex gap-2 flex-wrap"
              role="group"
              aria-label="Filter subscription plans by billing interval"
            >
              <button
                onClick={() => handleFilterChange({ interval: undefined })}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50 ${
                  !filters.interval
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                aria-pressed={!filters.interval}
                aria-label="Show all subscription plans and credit packs"
              >
                All Plans
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'monthly' })}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50 ${
                  filters.interval === 'monthly'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                aria-pressed={filters.interval === 'monthly'}
                aria-label="Show monthly subscription plans"
              >
                Monthly
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'yearly' })}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50 ${
                  filters.interval === 'yearly'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                aria-pressed={filters.interval === 'yearly'}
                aria-label="Show yearly subscription plans"
              >
                Yearly
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'one-time' })}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-opacity-50 ${
                  filters.interval === 'one-time'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                aria-pressed={filters.interval === 'one-time'}
                aria-label="Show one-time credit pack purchases"
              >
                Credit Packs
              </button>
            </div>

            {/* Clear filters */}
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                Clear filters
              </button>
            )}
          </div>

          {/* Results count */}
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {plans.length} plan{plans.length !== 1 ? 's' : ''} available
          </p>
        </div>
      )}

      {/* Recurring Subscriptions Section */}
      {recurringPlans.length > 0 && (
        <div className="mb-8">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Recurring Subscriptions
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Monthly and yearly plans with ongoing access to premium features
            </p>
          </div>

          <div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
            role="grid"
            aria-label="Recurring subscription plans"
          >
            {recurringPlans.map((plan) => (
              <div key={plan.id} role="gridcell">
                <SubscriptionPlanCard
                  plan={plan}
                  isCurrentPlan={currentPlanId === plan.id}
                  isPopular={popularPlanId === plan.id}
                  onSubscribe={onSubscribe}
                  isLoading={externalLoading}
                  disabled={currentPlanId === plan.id}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* One-Time Payment Packs Section */}
      {oneTimePlans.length > 0 && (
        <div className="mb-8">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Credit Packs
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              One-time purchases for additional credits without ongoing commitment
            </p>
          </div>

          <div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
            role="grid"
            aria-label="One-time credit pack plans"
          >
            {oneTimePlans.map((plan) => (
              <div key={plan.id} role="gridcell">
                <SubscriptionPlanCard
                  plan={plan}
                  isCurrentPlan={currentPlanId === plan.id}
                  isPopular={popularPlanId === plan.id}
                  onSubscribe={onSubscribe}
                  isLoading={externalLoading}
                  disabled={currentPlanId === plan.id}
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No Plans Message */}
      {recurringPlans.length === 0 && oneTimePlans.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No Plans Available
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            {hasActiveFilters
              ? 'No plans match your current filters. Try adjusting your search criteria.'
              : 'There are currently no subscription plans available.'
            }
          </p>
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 text-sm font-medium"
            >
              Clear Filters
            </button>
          )}
        </div>
      )}

      {/* Plan comparison note */}
      {plans.length > 1 && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            All plans include our core features. Higher tiers provide more credits and advanced capabilities.
          </p>
        </div>
      )}
    </div>
  );
}
