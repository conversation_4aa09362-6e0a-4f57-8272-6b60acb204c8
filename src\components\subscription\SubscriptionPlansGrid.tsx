import React, { useState } from 'react';
import SubscriptionPlanCard from './SubscriptionPlanCard';
import { Subscription, SubscriptionFilters } from '../../types';
import { useFilteredSubscriptionPlans } from '../../hooks/useSubscriptions';
import { ErrorDisplay } from '../error';

interface SubscriptionPlansGridProps {
  onSubscribe: (planId: string) => void;
  currentPlanId?: string;
  isLoading?: boolean;
  className?: string;
  showFilters?: boolean;
  popularPlanId?: string;
}

export default function SubscriptionPlansGrid({
  onSubscribe,
  currentPlanId,
  isLoading: externalLoading = false,
  className = '',
  showFilters = true,
  popularPlanId,
}: SubscriptionPlansGridProps) {
  const [filters, setFilters] = useState<SubscriptionFilters>({
    isActive: true,
  });

  const { 
    data: plans = [], 
    isLoading: plansLoading, 
    error 
  } = useFilteredSubscriptionPlans(filters);

  const isLoading = plansLoading || externalLoading;

  const handleFilterChange = (newFilters: Partial<SubscriptionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({ isActive: true });
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    key !== 'isActive' && filters[key as keyof SubscriptionFilters] !== undefined
  );

  // Loading skeleton
  if (isLoading) {
    return (
      <div className={className}>
        {showFilters && (
          <div className="mb-6">
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse mb-4" />
            <div className="flex gap-2">
              {[1, 2, 3].map(i => (
                <div key={i} className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
              ))}
            </div>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-96 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={className}>
        <ErrorDisplay 
          error={error} 
          title="Failed to load subscription plans"
          description="Please try again or contact support if the problem persists."
        />
      </div>
    );
  }

  // Empty state
  if (!plans || plans.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No subscription plans available
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          {hasActiveFilters 
            ? 'No plans match your current filters. Try adjusting your search criteria.'
            : 'There are currently no subscription plans available.'
          }
        </p>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-brand-600 hover:text-brand-700 dark:text-brand-400 dark:hover:text-brand-300 text-sm font-medium"
          >
            Clear filters
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Filters */}
      {showFilters && (
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            {/* Interval Filter */}
            <div className="flex gap-2">
              <button
                onClick={() => handleFilterChange({ interval: undefined })}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  !filters.interval
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                All
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'monthly' })}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filters.interval === 'monthly'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'yearly' })}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filters.interval === 'yearly'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Yearly
              </button>
              <button
                onClick={() => handleFilterChange({ interval: 'one-time' })}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  filters.interval === 'one-time'
                    ? 'bg-brand-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                One-time
              </button>
            </div>

            {/* Clear filters */}
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                Clear filters
              </button>
            )}
          </div>

          {/* Results count */}
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {plans.length} plan{plans.length !== 1 ? 's' : ''} available
          </p>
        </div>
      )}

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <SubscriptionPlanCard
            key={plan.id}
            plan={plan}
            isCurrentPlan={currentPlanId === plan.id}
            isPopular={popularPlanId === plan.id}
            onSubscribe={onSubscribe}
            isLoading={externalLoading}
            disabled={currentPlanId === plan.id}
          />
        ))}
      </div>

      {/* Plan comparison note */}
      {plans.length > 1 && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            All plans include our core features. Higher tiers provide more credits and advanced capabilities.
          </p>
        </div>
      )}
    </div>
  );
}
