import PageMeta from "../../components/common/PageMeta";
import { ProfileCompletionExample } from "../../components/profile-completion";
import ActiveSessionWidget from "../../components/dashboard/ActiveSessionWidget";
import TodayAppointments from "../../components/dashboard/TodayAppointments";
import DashboardMetrics from "../../components/dashboard/DashboardMetrics";
import QuickActions from "../../components/provider/QuickActions";
import RecentAppointmentsSimple from "../../components/dashboard/RecentAppointmentsSimple";
import RevenueChart from "../../components/dashboard/RevenueChart";
import PendingAppointments from "../../components/dashboard/PendingAppointments";
import SubscriptionWidget from "../../components/dashboard/SubscriptionWidget";

import { useAuth } from "../../context/AuthContext";

export default function Home() {
  const { user } = useAuth();

  return (
    <>
      <PageMeta
        title="Provider Dashboard | Manage Your Business"
        description="Provider dashboard with appointments, services, and business metrics"
      />



      {/* Profile Completion Card */}
      {user && (
        <ProfileCompletionExample
          userId={user.id}
          showDetails={false}
          className="mb-6"
        />
      )}

      <div className="grid grid-cols-12 gap-4 md:gap-6">
        {/* Priority 1: Essential Operational Widgets */}
        <div className="col-span-12 xl:col-span-4 space-y-6">
          <SubscriptionWidget />
          <ActiveSessionWidget />
          <PendingAppointments />
          <TodayAppointments />
          <QuickActions />
        </div>

        {/* Priority 2: Key Metrics and Charts */}
        <div className="col-span-12 xl:col-span-8 space-y-6">
          <DashboardMetrics />
          <RevenueChart />
          <RecentAppointmentsSimple />
        </div>
      </div>
    </>
  );
}
