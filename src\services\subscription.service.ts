import { apiClient } from '../lib/api-client';
import { config } from '../lib/config';
import {
  Subscription,
  UserSubscription,
  SubscriptionPlansResponse,
  UserSubscriptionResponse,
  SubscribeResponse,
  SubscribeRequest,
  GetSubscriptionsQuery,
  SubscriptionFilters,
  SubscriptionStats,
} from '../types';

/**
 * Subscription management API service
 */
export class SubscriptionService {
  /**
   * Get all available subscription plans (public endpoint)
   */
  static async getSubscriptionPlans(query?: GetSubscriptionsQuery): Promise<SubscriptionPlansResponse> {
    const response = await apiClient.get<SubscriptionPlansResponse>(
      config.endpoints.subscriptions.plans,
      { params: query }
    );
    return response.data;
  }

  /**
   * Get subscription plans with filtering
   */
  static async getFilteredSubscriptionPlans(filters?: SubscriptionFilters): Promise<Subscription[]> {
    const query: GetSubscriptionsQuery = {
      isActive: filters?.isActive,
      page: 1,
      limit: 100, // Get all plans for filtering
    };

    const response = await this.getSubscriptionPlans(query);
    let plans = response.data.subscriptions;

    // Apply client-side filtering
    if (filters) {
      if (filters.interval) {
        plans = plans.filter(plan => plan.interval === filters.interval);
      }
      if (filters.minPrice !== undefined) {
        plans = plans.filter(plan => plan.price >= filters.minPrice!);
      }
      if (filters.maxPrice !== undefined) {
        plans = plans.filter(plan => plan.price <= filters.maxPrice!);
      }
      if (filters.minCredits !== undefined) {
        plans = plans.filter(plan => plan.creditsIncluded >= filters.minCredits!);
      }
      if (filters.maxCredits !== undefined) {
        plans = plans.filter(plan => plan.creditsIncluded <= filters.maxCredits!);
      }
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        plans = plans.filter(plan => 
          plan.name.toLowerCase().includes(searchTerm) ||
          plan.description.toLowerCase().includes(searchTerm) ||
          plan.features.some(feature => feature.toLowerCase().includes(searchTerm))
        );
      }
    }

    return plans;
  }

  /**
   * Get current user's subscription status (authenticated endpoint)
   */
  static async getUserSubscription(): Promise<UserSubscriptionResponse> {
    const response = await apiClient.get<UserSubscriptionResponse>(
      config.endpoints.subscriptions.userSubscription
    );
    return response.data;
  }

  /**
   * Subscribe to a subscription plan (authenticated endpoint)
   */
  static async subscribeToplan(data: SubscribeRequest): Promise<SubscribeResponse> {
    const response = await apiClient.post<SubscribeResponse>(
      config.endpoints.subscriptions.subscribe,
      data
    );
    return response.data;
  }

  /**
   * Get subscription by ID (from available plans)
   */
  static async getSubscriptionById(id: string): Promise<Subscription | null> {
    try {
      const response = await this.getSubscriptionPlans({ isActive: true });
      const plan = response.data.subscriptions.find(sub => sub.id === id);
      return plan || null;
    } catch (error) {
      console.error('Error fetching subscription by ID:', error);
      return null;
    }
  }

  /**
   * Get subscription statistics (if needed for admin/analytics)
   */
  static async getSubscriptionStats(): Promise<SubscriptionStats> {
    // This would be an admin endpoint - for now return mock data
    // In a real implementation, this would call an admin API endpoint
    const plansResponse = await this.getSubscriptionPlans({ isActive: true });
    const userSubResponse = await this.getUserSubscription();

    return {
      totalPlans: plansResponse.data.subscriptions.length,
      activePlans: plansResponse.data.subscriptions.filter(p => p.isActive).length,
      totalSubscriptions: 1, // Would come from API
      activeSubscriptions: userSubResponse.data.subscription ? 1 : 0,
      totalRevenue: 0, // Would come from API
      averagePrice: plansResponse.data.subscriptions.reduce((sum, plan) => sum + plan.price, 0) / plansResponse.data.subscriptions.length,
      mostPopularPlan: plansResponse.data.subscriptions[0], // Would come from API
      recentSubscriptions: userSubResponse.data.subscription ? [userSubResponse.data.subscription] : [],
    };
  }

  /**
   * Check if user has an active subscription
   */
  static async hasActiveSubscription(): Promise<boolean> {
    try {
      const response = await this.getUserSubscription();
      return response.data.subscription?.status === 'active';
    } catch (error) {
      console.error('Error checking subscription status:', error);
      return false;
    }
  }

  /**
   * Get user's current credit balance
   */
  static async getCreditBalance(): Promise<number> {
    try {
      const response = await this.getUserSubscription();
      return response.data.credits || 0;
    } catch (error) {
      console.error('Error fetching credit balance:', error);
      return 0;
    }
  }

  /**
   * Get subscription status information for UI display
   */
  static async getSubscriptionStatusInfo(): Promise<{
    hasActiveSubscription: boolean;
    currentPlan?: Subscription;
    creditsRemaining: number;
    expiryDate?: string;
    status: 'active' | 'expired' | 'cancelled' | 'pending' | 'none';
    daysUntilExpiry?: number;
    isExpiringSoon: boolean;
    canUpgrade: boolean;
    canDowngrade: boolean;
  }> {
    try {
      const userSubResponse = await this.getUserSubscription();
      const userSub = userSubResponse.data.subscription;
      
      if (!userSub) {
        return {
          hasActiveSubscription: false,
          creditsRemaining: userSubResponse.data.credits || 0,
          status: 'none',
          isExpiringSoon: false,
          canUpgrade: true,
          canDowngrade: false,
        };
      }

      const now = new Date();
      const expiryDate = userSub.endDate ? new Date(userSub.endDate) : null;
      const daysUntilExpiry = expiryDate ? Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : undefined;
      const isExpiringSoon = daysUntilExpiry !== undefined && daysUntilExpiry <= 7 && daysUntilExpiry > 0;

      return {
        hasActiveSubscription: userSub.status === 'active',
        currentPlan: userSub.subscription,
        creditsRemaining: userSubResponse.data.credits || 0,
        expiryDate: userSub.endDate,
        status: userSub.status,
        daysUntilExpiry,
        isExpiringSoon,
        canUpgrade: userSub.status === 'active',
        canDowngrade: userSub.status === 'active',
      };
    } catch (error) {
      console.error('Error fetching subscription status info:', error);
      return {
        hasActiveSubscription: false,
        creditsRemaining: 0,
        status: 'none',
        isExpiringSoon: false,
        canUpgrade: true,
        canDowngrade: false,
      };
    }
  }
}
