import React, { useState } from 'react';
import Button from '../ui/button/Button';
import { Modal } from '../ui/modal';
import ConfirmationDialog from '../ui/confirmation/ConfirmationDialog';
import SubscriptionPlanList from './SubscriptionPlanList';
import {
  useSubscriptionPlans,
  useUserSubscription,
  useSubscribeToplan
} from '../../hooks/useSubscriptions';
import { Subscription } from '../../types';
import { SubscriptionPlansGridSkeleton, SubscriptionNetworkError } from './';

interface SubscriptionPlanSelectionFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (subscription: any) => void;
  preselectedPlanId?: string;
  className?: string;
}

export default function SubscriptionPlanSelectionFlow({
  isOpen,
  onClose,
  onSuccess,
  preselectedPlanId,
  className = '',
}: SubscriptionPlanSelectionFlowProps) {
  const [currentStep, setCurrentStep] = useState<'selection' | 'confirmation' | 'processing' | 'success'>('selection');
  const [selectedPlan, setSelectedPlan] = useState<Subscription | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const { data: plansData, isLoading: plansLoading } = useSubscriptionPlans({ isActive: true });
  const { data: userSubscriptionData } = useUserSubscription();
  const subscribeToplanMutation = useSubscribeToplan();

  const plans = plansData?.data.subscriptions || [];
  const userSubscription = userSubscriptionData?.data.subscription;
  const currentPlanId = userSubscription?.subscriptionId;

  const handlePlanSelect = (planId: string) => {
    const plan = plans.find(p => p.id === planId);
    if (plan) {
      setSelectedPlan(plan);
      setShowConfirmation(true);
    }
  };

  const handleConfirmSubscription = async () => {
    if (!selectedPlan) return;

    setCurrentStep('processing');
    setShowConfirmation(false);

    try {
      const result = await subscribeToplanMutation.mutateAsync({
        subscriptionId: selectedPlan.id,
      });

      setCurrentStep('success');
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Auto-close after showing success for 3 seconds
      setTimeout(() => {
        handleClose();
      }, 3000);

    } catch (error) {
      // Error is handled by the mutation (toast notification)
      setCurrentStep('selection');
    }
  };

  const handleClose = () => {
    setCurrentStep('selection');
    setSelectedPlan(null);
    setShowConfirmation(false);
    onClose();
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price / 100);
  };

  const getStepTitle = (): string => {
    switch (currentStep) {
      case 'selection':
        return 'Choose Your Plan';
      case 'confirmation':
        return 'Confirm Your Selection';
      case 'processing':
        return 'Processing Subscription';
      case 'success':
        return 'Subscription Activated!';
      default:
        return 'Subscription';
    }
  };

  const getStepDescription = (): string => {
    switch (currentStep) {
      case 'selection':
        return 'Select the perfect plan for your business needs. You can upgrade or downgrade anytime.';
      case 'confirmation':
        return 'Review your selected plan details before confirming your subscription.';
      case 'processing':
        return 'Please wait while we activate your subscription...';
      case 'success':
        return 'Your subscription has been successfully activated. You can now enjoy all the benefits!';
      default:
        return '';
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'selection':
        if (plansLoading) {
          return <SubscriptionPlansGridSkeleton count={4} />;
        }

        if (plansError) {
          return (
            <SubscriptionNetworkError
              onRetry={() => window.location.reload()}
              operation="load subscription plans"
            />
          );
        }

        return (
          <SubscriptionPlanList
            plans={plans || []}
            currentPlanId={currentPlanId}
            onSelectPlan={handlePlanSelect}
            isLoading={subscribeToplanMutation.isPending}
          />
        );

      case 'processing':
        return (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-brand-500 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Activating Your Subscription
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              This will only take a moment...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Welcome to {selectedPlan?.name}!
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Your subscription is now active and you have {selectedPlan?.creditsIncluded.toLocaleString()} credits available.
            </p>
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-brand-600 dark:text-brand-400">
                  {selectedPlan?.creditsIncluded.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Credits Added to Your Account
                </div>
              </div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              This window will close automatically in a few seconds...
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        className={`max-w-2xl p-0 ${className}`}
        showCloseButton={currentStep !== 'processing'}
      >
        <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
          {/* Header */}
          <div className="p-6 pb-4">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
              {currentStep === 'selection' ? 'Choose a Plan' : getStepTitle()}
            </h2>
            {currentStep === 'selection' && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Select the plan that best fits your needs.
                <button
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 ml-1"
                  onClick={() => {
                    // Handle view detailed plan information
                    window.open('/subscription-details', '_blank');
                  }}
                >
                  View detailed plan information
                </button>
              </p>
            )}
            {currentStep !== 'selection' && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {getStepDescription()}
              </p>
            )}
          </div>

          {/* Content */}
          <div className="px-6 pb-4 max-h-[70vh] overflow-y-auto">
            {renderStepContent()}
          </div>

          {/* Footer */}
          {currentStep === 'selection' && (
            <div className="p-6 pt-4 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClose}
                disabled={subscribeToplanMutation.isPending}
              >
                Cancel
              </Button>
            </div>
          )}
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmSubscription}
        title="Confirm Subscription"
        message={selectedPlan ? `Are you sure you want to subscribe to ${selectedPlan.name} for ${formatPrice(selectedPlan.price)}${selectedPlan.interval !== 'one-time' ? `/${selectedPlan.interval}` : ''}? Your subscription will be activated immediately and you'll receive ${selectedPlan.creditsIncluded.toLocaleString()} credits.` : ''}
        confirmText="Subscribe Now"
        cancelText="Cancel"
        isLoading={subscribeToplanMutation.isPending}
        variant="info"
      />
    </>
  );
}
