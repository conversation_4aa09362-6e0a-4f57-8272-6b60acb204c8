import React from 'react';
import { Link } from 'react-router';
import Button from '../ui/button/Button';
import { useUserSubscription, useSubscriptionStatusInfo } from '../../hooks/useSubscriptions';

export default function SubscriptionWidget() {
  const { data: userSubscriptionData, isLoading } = useUserSubscription();
  const { data: statusInfo } = useSubscriptionStatusInfo();

  const userSubscription = userSubscriptionData?.data.subscription;
  const creditsRemaining = userSubscriptionData?.data.credits || 0;

  const getCreditStatusColor = (credits: number): string => {
    if (credits <= 10) {
      return 'text-red-600 dark:text-red-400';
    } else if (credits <= 50) {
      return 'text-yellow-600 dark:text-yellow-400';
    }
    return 'text-green-600 dark:text-green-400';
  };

  const getCreditStatusBg = (credits: number): string => {
    if (credits <= 10) {
      return 'bg-red-50 dark:bg-red-900/20';
    } else if (credits <= 50) {
      return 'bg-yellow-50 dark:bg-yellow-900/20';
    }
    return 'bg-green-50 dark:bg-green-900/20';
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  // No subscription case
  if (!userSubscription) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Subscription
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No active plan
            </p>
          </div>
          <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        </div>

        {/* Credits */}
        <div className={`rounded-lg p-4 mb-4 ${getCreditStatusBg(creditsRemaining)}`}>
          <div className="flex items-center justify-between">
            <div>
              <div className={`text-2xl font-bold ${getCreditStatusColor(creditsRemaining)}`}>
                {creditsRemaining.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Credits remaining
              </div>
            </div>
            {creditsRemaining <= 10 && (
              <div className="text-right">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                  Low credits
                </span>
              </div>
            )}
          </div>
        </div>

        <Link to="/subscription">
          <Button variant="primary" size="sm" className="w-full">
            Choose a Plan
          </Button>
        </Link>
      </div>
    );
  }

  // Active subscription case
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getDaysUntilExpiry = (endDate?: string): number | null => {
    if (!endDate) return null;
    const now = new Date();
    const expiry = new Date(endDate);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilExpiry = getDaysUntilExpiry(userSubscription.endDate);
  const isExpiringSoon = daysUntilExpiry !== null && daysUntilExpiry <= 7 && daysUntilExpiry > 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Subscription
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {userSubscription.subscription?.name || 'Active Plan'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(userSubscription.status)}`}>
            {userSubscription.status === 'active' ? 'Active' : userSubscription.status}
          </span>
          {isExpiringSoon && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400">
              Expires soon
            </span>
          )}
        </div>
      </div>

      {/* Credits */}
      <div className={`rounded-lg p-4 mb-4 ${getCreditStatusBg(creditsRemaining)}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className={`text-2xl font-bold ${getCreditStatusColor(creditsRemaining)}`}>
              {creditsRemaining.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Credits remaining
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {userSubscription.creditsAllocated.toLocaleString()}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Total allocated
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
            <div
              className="bg-brand-500 h-1.5 rounded-full transition-all duration-300"
              style={{
                width: `${Math.max(0, Math.min(100, (creditsRemaining / userSubscription.creditsAllocated) * 100))}%`
              }}
            />
          </div>
        </div>
      </div>

      {/* Expiry info */}
      {userSubscription.endDate && (
        <div className="flex items-center justify-between text-sm mb-4">
          <span className="text-gray-500 dark:text-gray-400">
            {userSubscription.status === 'active' ? 'Expires' : 'Expired'}
          </span>
          <span className={`font-medium ${isExpiringSoon ? 'text-orange-600 dark:text-orange-400' : 'text-gray-900 dark:text-white'}`}>
            {formatDate(userSubscription.endDate)}
            {daysUntilExpiry !== null && daysUntilExpiry > 0 && (
              <span className="ml-1 text-xs">
                ({daysUntilExpiry}d)
              </span>
            )}
          </span>
        </div>
      )}

      {/* Action button */}
      <Link to="/subscription">
        <Button 
          variant={userSubscription.status === 'active' ? 'outline' : 'primary'} 
          size="sm" 
          className="w-full"
        >
          {userSubscription.status === 'active' ? 'Manage Plan' : 'Renew Plan'}
        </Button>
      </Link>
    </div>
  );
}
