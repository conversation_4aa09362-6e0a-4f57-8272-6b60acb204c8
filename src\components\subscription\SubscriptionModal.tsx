import React, { useState } from 'react';
import { Modal } from '../ui/modal';
import Button from '../ui/button/Button';
import SubscriptionPlanList from './SubscriptionPlanList';
import SubscriptionStatus from './SubscriptionStatus';
import { SubscriptionPlansGridSkeleton, SubscriptionNetworkError } from './';
import { useSubscriptionPlans, useUserSubscription, useSubscribeToplan } from '../../hooks/useSubscriptions';
import { useConfirmation } from '../../hooks/useConfirmation';
import ConfirmationDialog from '../ui/confirmation/ConfirmationDialog';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode?: 'select' | 'manage' | 'upgrade';
  className?: string;
}

export default function SubscriptionModal({
  isOpen,
  onClose,
  mode = 'select',
  className = '',
}: SubscriptionModalProps) {
  const [currentView, setCurrentView] = useState<'plans' | 'status' | 'confirmation'>(
    mode === 'manage' ? 'status' : 'plans'
  );
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);

  const { data: plansData, isLoading: plansLoading, error: plansError } = useSubscriptionPlans({ isActive: true });
  const { data: userSubscriptionData, isLoading: subscriptionLoading } = useUserSubscription();
  const subscribeToplanMutation = useSubscribeToplan();
  const { isOpen: confirmationOpen, openConfirmation, closeConfirmation } = useConfirmation();

  const plans = plansData?.data.subscriptions || [];

  const userSubscription = userSubscriptionData?.data.subscription;
  const creditsRemaining = userSubscriptionData?.data.credits || 0;

  const formatPrice = (price: number): string => {
    if (price === 0) return 'Free';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(price / 100);
  };

  const handleSubscribe = (planId: string) => {
    setSelectedPlanId(planId);
    openConfirmation();
  };

  const handleConfirmSubscription = async () => {
    if (!selectedPlanId) return;

    try {
      await subscribeToplanMutation.mutateAsync({
        subscriptionId: selectedPlanId,
      });
      closeConfirmation();
      setCurrentView('status');
      // Don't close the modal immediately, let user see the success state
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      // Error is handled by the mutation
      closeConfirmation();
    }
  };

  const handleUpgrade = () => {
    setCurrentView('plans');
  };

  const handleManage = () => {
    setCurrentView('status');
  };

  const getModalTitle = (): string => {
    switch (currentView) {
      case 'plans':
        return mode === 'upgrade' ? 'Upgrade Your Plan' : 'Choose Your Plan';
      case 'status':
        return 'Subscription Management';
      case 'confirmation':
        return 'Confirm Subscription';
      default:
        return 'Subscription';
    }
  };

  const getModalDescription = (): string => {
    switch (currentView) {
      case 'plans':
        return mode === 'upgrade' 
          ? 'Select a new plan to upgrade your subscription and get more credits.'
          : 'Select the perfect plan for your needs. You can upgrade or downgrade anytime.';
      case 'status':
        return 'Manage your current subscription and view your credit balance.';
      case 'confirmation':
        return 'Please confirm your subscription details before proceeding.';
      default:
        return '';
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'plans':
        if (plansLoading) {
          return <SubscriptionPlansGridSkeleton count={4} />;
        }

        if (plansError) {
          return (
            <SubscriptionNetworkError
              onRetry={() => window.location.reload()}
              operation="load subscription plans"
            />
          );
        }

        return (
          <SubscriptionPlanList
            plans={plans}
            currentPlanId={userSubscription?.subscriptionId}
            onSelectPlan={handleSubscribe}
            isLoading={subscribeToplanMutation.isPending}
          />
        );

      case 'status':
        return (
          <div className="space-y-6">
            <SubscriptionStatus
              subscription={userSubscription}
              creditsRemaining={creditsRemaining}
              onUpgrade={handleUpgrade}
              onManage={() => setCurrentView('status')}
              isLoading={subscriptionLoading}
            />
            
            {/* Additional management options */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Subscription Actions
              </h4>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  size="md"
                  onClick={handleUpgrade}
                  className="w-full"
                >
                  View All Plans
                </Button>
                
                {userSubscription?.status === 'active' && (
                  <Button
                    variant="outline"
                    size="md"
                    onClick={() => {
                      // This would open a cancellation flow
                      console.log('Cancel subscription');
                    }}
                    className="w-full text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Cancel Subscription
                  </Button>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        className={`${currentView === 'plans' ? 'max-w-2xl' : 'max-w-4xl'} p-0 ${className}`}
      >
        <div className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden">
          {/* Header */}
          <div className="p-6 pb-4">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
              {currentView === 'plans' ? 'Choose a Plan' : getModalTitle()}
            </h2>
            {currentView === 'plans' && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Select the plan that best fits your needs.
                <button
                  className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 ml-1"
                  onClick={() => {
                    // Handle view detailed plan information
                    window.open('/subscription-details', '_blank');
                  }}
                >
                  View detailed plan information
                </button>
              </p>
            )}
            {currentView !== 'plans' && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {getModalDescription()}
              </p>
            )}

            {/* Navigation tabs */}
            {userSubscription && (
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mt-4">
                <button
                  onClick={() => setCurrentView('status')}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    currentView === 'status'
                      ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  Current Plan
                </button>
                <button
                  onClick={() => setCurrentView('plans')}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    currentView === 'plans'
                      ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  All Plans
                </button>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="px-6 pb-4 max-h-[70vh] overflow-y-auto">
            {renderContent()}
          </div>

          {/* Footer */}
          {currentView === 'plans' && (
            <div className="p-6 pt-4 flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
              >
                Cancel
              </Button>
            </div>
          )}

          {currentView !== 'plans' && (
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  All plans include 24/7 support and can be cancelled anytime.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onClose}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationOpen}
        onClose={closeConfirmation}
        onConfirm={handleConfirmSubscription}
        title="Confirm Subscription"
        message={(() => {
          const selectedPlan = plans.find(p => p.id === selectedPlanId);
          if (!selectedPlan) return "Are you sure you want to subscribe to this plan?";

          const priceText = selectedPlan.price === 0
            ? 'Free'
            : `${formatPrice(selectedPlan.price)}${selectedPlan.interval !== 'one-time' ? `/${selectedPlan.interval}` : ''}`;

          return `Are you sure you want to subscribe to ${selectedPlan.name} for ${priceText}? Your subscription will be activated immediately and you'll receive ${selectedPlan.creditsIncluded.toLocaleString()} credits.`;
        })()}
        confirmText="Subscribe Now"
        cancelText="Cancel"
        isLoading={subscribeToplanMutation.isPending}
        variant="primary"
      />
    </>
  );
}
