import React, { useState } from 'react';
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import PageMeta from "../../components/common/PageMeta";
import Button from "../../components/ui/button/Button";
import { useModal } from "../../hooks/useModal";
import { ErrorDisplay } from "../../components/error";
import { 
  useUserSubscription, 
  useSubscriptionPlans, 
  useSubscriptionStatusInfo 
} from "../../hooks/useSubscriptions";
import {
  SubscriptionStatus,
  SubscriptionPlanList,
  SubscriptionModal,
  SubscriptionManagementActions,
  SubscriptionPlansGridSkeleton,
  SubscriptionNetworkError
} from "../../components/subscription";

export default function SubscriptionManagement() {
  const [modalMode, setModalMode] = useState<'select' | 'manage' | 'upgrade'>('select');
  const { isOpen, openModal, closeModal } = useModal();

  const { data: userSubscriptionData, isLoading: subscriptionLoading, error: subscriptionError } = useUserSubscription();
  const { data: plansData, isLoading: plansLoading, error: plansError } = useSubscriptionPlans({ isActive: true });
  const { data: statusInfo, isLoading: statusLoading } = useSubscriptionStatusInfo();

  const userSubscription = userSubscriptionData?.data.subscription;
  const creditsRemaining = userSubscriptionData?.data.credits || 0;
  const plans = plansData?.data.subscriptions || [];

  const handleOpenSubscriptionModal = (mode: 'select' | 'manage' | 'upgrade' = 'select') => {
    setModalMode(mode);
    openModal();
  };

  const handleSubscribe = (planId: string) => {
    // This will be handled by the modal component
    console.log('Subscribe to plan:', planId);
  };

  const isLoading = subscriptionLoading || plansLoading || statusLoading;
  const error = subscriptionError || plansError;

  if (isLoading) {
    return (
      <>
        <PageMeta
          title="Subscription Management | Provider Dashboard"
          description="Manage your subscription plan and billing"
        />
        <PageBreadcrumb pageTitle="Subscription" />
        
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500"></div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <PageMeta
          title="Subscription Management | Provider Dashboard"
          description="Manage your subscription plan and billing"
        />
        <PageBreadcrumb pageTitle="Subscription" />
        
        <div className="p-6">
          <ErrorDisplay
            error={error}
            title="Failed to load subscription data"
            variant="card"
            showRetry
            onRetry={() => window.location.reload()}
          />
        </div>
      </>
    );
  }

  return (
    <>
      <PageMeta
        title="Subscription Management | Provider Dashboard"
        description="Manage your subscription plan, view billing history, and track credit usage"
      />
      <PageBreadcrumb pageTitle="Subscription" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Subscription Management
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your subscription plan, view billing history, and track credit usage
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            {userSubscription ? (
              <>
                <Button
                  onClick={() => handleOpenSubscriptionModal('upgrade')}
                  variant="outline"
                  size="sm"
                >
                  View All Plans
                </Button>
                <Button
                  onClick={() => handleOpenSubscriptionModal('manage')}
                  size="sm"
                >
                  Manage Subscription
                </Button>
              </>
            ) : (
              <Button
                onClick={() => handleOpenSubscriptionModal('select')}
                size="sm"
              >
                Choose a Plan
              </Button>
            )}
          </div>
        </div>

        {/* Current Subscription Status */}
        {userSubscription && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-3">
              <SubscriptionStatus
                subscription={userSubscription}
                creditsRemaining={creditsRemaining}
                onUpgrade={() => handleOpenSubscriptionModal('upgrade')}
                onManage={() => handleOpenSubscriptionModal('manage')}
                isLoading={subscriptionLoading}
              />
            </div>
          
          {/* Quick Stats and Actions */}
          <div className="space-y-4">
            {/* Credit Usage Card */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Credit Usage
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Available</span>
                  <span className="text-lg font-bold text-green-600 dark:text-green-400">
                    {creditsRemaining.toLocaleString()}
                  </span>
                </div>
                {userSubscription && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Total Allocated</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {userSubscription.creditsAllocated.toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Subscription Info Card */}
            {statusInfo && (
              <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Quick Info
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Status</span>
                    <span className={`text-sm font-medium ${
                      statusInfo.hasActiveSubscription
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {statusInfo.hasActiveSubscription ? 'Active' : 'No Plan'}
                    </span>
                  </div>
                  {statusInfo.daysUntilExpiry && statusInfo.daysUntilExpiry > 0 && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Expires in</span>
                      <span className={`text-sm font-medium ${
                        statusInfo.isExpiringSoon
                          ? 'text-orange-600 dark:text-orange-400'
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {statusInfo.daysUntilExpiry} days
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Subscription Management Actions */}
            <SubscriptionManagementActions />
          </div>
        </div>
        )}

        {/* Welcome Section for New Users */}
        {!userSubscription && (
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200 dark:border-blue-800 p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Welcome to Dalti Provider
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
              Choose a subscription plan to unlock powerful features for managing your business.
              All plans include credits for appointments, customer management, and advanced analytics.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={() => handleOpenSubscriptionModal('select')}
                size="md"
                className="px-8"
              >
                Get Started
              </Button>
              <Button
                variant="outline"
                size="md"
                onClick={() => {
                  // Scroll to plans section
                  document.getElementById('plans-section')?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                View Plans Below
              </Button>
            </div>
          </div>
        )}

        {/* Available Plans Section */}
        <div id="plans-section" className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                {userSubscription ? 'All Available Plans' : 'Choose a Plan'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {userSubscription
                  ? 'Compare all available plans and upgrade or change your subscription'
                  : 'Select the plan that best fits your needs'
                }
              </p>
            </div>
            {userSubscription && (
              <Button
                onClick={() => handleOpenSubscriptionModal('manage')}
                variant="outline"
                size="sm"
              >
                Manage Current Plan
              </Button>
            )}
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
            {plansLoading ? (
              <SubscriptionPlansGridSkeleton count={4} />
            ) : plansError ? (
              <SubscriptionNetworkError
                onRetry={() => window.location.reload()}
                operation="load subscription plans"
              />
            ) : (
              <SubscriptionPlanList
                plans={plans}
                currentPlanId={userSubscription?.subscriptionId}
                onSelectPlan={handleSubscribe}
                isLoading={false}
              />
            )}
          </div>
        </div>

        {/* Billing History Section */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Billing History
          </h3>
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <p className="text-gray-500 dark:text-gray-400">
              {userSubscription 
                ? 'Billing history will appear here once available.'
                : 'Subscribe to a plan to view billing history.'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={isOpen}
        onClose={closeModal}
        mode={modalMode}
      />
    </>
  );
}
