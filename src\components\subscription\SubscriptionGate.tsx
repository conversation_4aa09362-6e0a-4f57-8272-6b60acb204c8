import React from 'react';
import Button from '../ui/button/Button';
import { useSubscriptionGate } from '../../hooks/useSubscriptionMonitoring';
import { useModal } from '../../hooks/useModal';
import { SubscriptionPlanSelectionFlow } from './';

interface SubscriptionGateProps {
  children: React.ReactNode;
  creditsRequired?: number;
  feature?: string;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  upgradeMessage?: string;
  className?: string;
}

export default function SubscriptionGate({
  children,
  creditsRequired = 1,
  feature,
  fallback,
  showUpgradePrompt = true,
  upgradeMessage,
  className = '',
}: SubscriptionGateProps) {
  const { canPerformAction, hasActiveSubscription, creditsRemaining } = useSubscriptionGate();
  const { isOpen, openModal, closeModal } = useModal();

  const canAccess = canPerformAction(creditsRequired);

  const getDefaultMessage = (): string => {
    if (!hasActiveSubscription) {
      return `You need an active subscription to access this feature. Choose a plan to continue.`;
    }
    
    if (creditsRemaining < creditsRequired) {
      return `You need ${creditsRequired} credits to use this feature. You have ${creditsRemaining} credits remaining. Upgrade your plan to get more credits.`;
    }

    return 'This feature requires a subscription upgrade.';
  };

  const handleUpgradeClick = () => {
    openModal();
  };

  if (canAccess) {
    return <>{children}</>;
  }

  // Custom fallback provided
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  // Default upgrade prompt
  if (showUpgradePrompt) {
    return (
      <>
        <div className={`bg-gray-50 dark:bg-gray-800/50 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 p-6 text-center ${className}`}>
          <div className="w-12 h-12 bg-brand-100 dark:bg-brand-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-brand-600 dark:text-brand-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {hasActiveSubscription ? 'Upgrade Required' : 'Subscription Required'}
          </h3>
          
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4 max-w-md mx-auto">
            {upgradeMessage || getDefaultMessage()}
          </p>

          {/* Credit info */}
          {hasActiveSubscription && (
            <div className="bg-white dark:bg-gray-700 rounded-lg p-3 mb-4 inline-block">
              <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-500 dark:text-gray-400">Credits remaining:</span>
                <span className={`font-semibold ${creditsRemaining <= 10 ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-white'}`}>
                  {creditsRemaining}
                </span>
              </div>
            </div>
          )}

          <Button
            onClick={handleUpgradeClick}
            size="sm"
            className="mx-auto"
          >
            {hasActiveSubscription ? 'Upgrade Plan' : 'Choose a Plan'}
          </Button>
        </div>

        <SubscriptionPlanSelectionFlow
          isOpen={isOpen}
          onClose={closeModal}
          onSuccess={() => {
            closeModal();
            // The page will automatically refresh due to query invalidation
          }}
        />
      </>
    );
  }

  // No access and no upgrade prompt - render nothing
  return null;
}

/**
 * Higher-order component version of SubscriptionGate
 */
export function withSubscriptionGate<P extends object>(
  Component: React.ComponentType<P>,
  gateProps?: Omit<SubscriptionGateProps, 'children'>
) {
  return function SubscriptionGatedComponent(props: P) {
    return (
      <SubscriptionGate {...gateProps}>
        <Component {...props} />
      </SubscriptionGate>
    );
  };
}


