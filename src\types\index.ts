/**
 * Central export file for all TypeScript types
 */

// Auth types
export * from './auth';

// Provider types
export * from './provider';

// Service types
export * from './service';

// Location types
export * from './location';

// Appointment types
export * from './appointment';

// Queue types
export * from './queue';

// Customer types
export * from './customer';

// Review types
export * from './review';

// Profile completion types
export * from './profile-completion';

// Subscription types
export * from './subscription';

// Common utility types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  errors?: any[];
}

export interface ApiError {
  message: string;
  status: number;
  errors?: any[];
}

// Form validation types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isSubmitting: boolean;
  isValid: boolean;
}

// UI state types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface ModalState {
  isOpen: boolean;
  data?: any;
}

// Dashboard types
export interface DashboardStats {
  appointments: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    upcoming: number;
  };
  revenue: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    thisYear: number;
  };
  customers: {
    total: number;
    new: number;
    returning: number;
    active: number;
  };
  services: {
    total: number;
    active: number;
    mostPopular: string;
  };
  queues: {
    active: number;
    totalWaiting: number;
    averageWaitTime: number;
  };
  reviews: {
    averageRating: number;
    totalReviews: number;
    pending: number;
  };
}

// Chart data types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
}

// Search and filter types
export interface SearchFilters {
  query?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string[];
  category?: string[];
  tags?: string[];
}

// File upload types
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  url?: string;
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  accentColor: string;
  fontSize: 'small' | 'medium' | 'large';
}

// Route types
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  protected?: boolean;
  roles?: string[];
  title?: string;
  description?: string;
}
